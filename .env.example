# Azure ML Configuration for train.py and score.py
# Copy this file to .env and fill in your Azure ML workspace details

# Azure subscription ID
AZURE_SUBSCRIPTION_ID=your-subscription-id-here

# Azure resource group name
AZURE_RESOURCE_GROUP=your-resource-group-name

# Azure ML workspace name
AZURE_ML_WORKSPACE_NAME=your-workspace-name

# Experiment name (optional, defaults to 'neural-network-training')
AZURE_ML_EXPERIMENT_NAME=neural-network-training

# Azure ML SDK v2 Model Configuration for score.py
# Model name in Azure ML registry (optional, defaults to 'tstarc-test')
AZURE_ML_MODEL_NAME=tstarc-test

# Model version (optional, defaults to latest version)
AZURE_ML_MODEL_VERSION=

# Legacy environment variables (for backward compatibility)
MODEL_NAME=tstarc-test
MODEL_VERSION=2

# Azure ML SDK v2 Deployment Configuration
# Environment name for deployment
ENV_NAME=ps-dev-claimsauto-tstarc-env

# Endpoint name for managed online endpoint
ENDPOINT_NAME=ps-dev-claimsauto-tstarc

# Deployment name for managed online deployment
DEPLOYMENT_NAME=ps-dev-claimsauto-tstarc

# Instance configuration for deployment
INSTANCE_TYPE=Standard_DS3_v2
INSTANCE_COUNT=1

# Deployment timeout in seconds (default: 1800 = 30 minutes)
TIMEOUT_SECONDS=1800

# Azure Storage Configuration (for utility files)
# Use either connection string OR account name + key
AZURE_STORAGE_CONNECTION_STRING=your-storage-connection-string
# OR
AZURE_STORAGE_ACCOUNT_NAME=your-storage-account-name
AZURE_STORAGE_ACCOUNT_KEY=your-storage-account-key

# Azure Authentication (for service principal authentication)
# These are used by DefaultAzureCredential when available
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret