[project]
name = "autolodge-retrained-deploy"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "azure-ai-ml>=1.27.1",
    "azure-identity>=1.23.0",
    "azureml-inference-server-http>=1.4.0",
    "jupyter>=1.1.1",
    "loguru>=0.7.3",
    "mlflow>=2.0.0",
    "nltk==3.7",
    "pandas>=2.2.3",
    "pendulum>=3.1.0",
    "protobuf==3.20",
    "pytest>=8.3.5",
    "python-dotenv>=1.1.0",
    "scikit-learn>=1.6.1",
    "symspellpy>=6.9.0",
    "tensorflow==2.8",
    "tqdm>=4.67.1",
]
