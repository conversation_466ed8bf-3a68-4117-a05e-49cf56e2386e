"""
Test script for Loguru logging configuration.

This script tests the Loguru logging setup without importing TensorFlow
to avoid protobuf compatibility issues during testing.
"""

import os
import json
from datetime import datetime
from pathlib import Path
from loguru import logger


def setup_logging():
    """Configure Loguru logging with timestamped files and rotation."""
    # Remove default handler
    logger.remove()
    
    # Create logs directory if it doesn't exist
    logs_dir = Path(__file__).parent / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Generate timestamp for log filename
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file = logs_dir / f"score_{timestamp}.log"
    
    # Add console handler with colored output
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
        colorize=True
    )
    
    # Add file handler with rotation
    logger.add(
        sink=str(log_file),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="1 day",  # Rotate daily
        retention="30 days",  # Keep logs for 30 days
        compression="zip",  # Compress old logs
        enqueue=True,  # Thread-safe logging
        backtrace=True,  # Include traceback in error logs
        diagnose=True   # Include variable values in traceback
    )
    
    # Add size-based rotation as backup
    logger.add(
        sink=str(logs_dir / f"score_size_rotated_{timestamp}.log"),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB",  # Rotate when file reaches 10MB
        retention=10,  # Keep 10 rotated files
        compression="zip",
        enqueue=True
    )
    
    logger.info(f"Logging configured - Log file: {log_file}")
    return log_file


def test_logging_functionality():
    """Test various logging levels and functionality."""
    
    # Initialize logging
    log_file = setup_logging()
    
    # Test different log levels
    logger.info("🚀 Starting Loguru logging test")
    logger.debug("This is a debug message - should appear in file but not console")
    logger.info("This is an info message - should appear in both console and file")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test structured logging with context
    user_data = {"user_id": 12345, "action": "login", "timestamp": datetime.now().isoformat()}
    logger.info("User action logged", extra={"user_data": user_data})
    
    # Test logging with variables
    items_processed = 150
    processing_time = 2.34
    logger.info(f"Processed {items_processed} items in {processing_time:.2f} seconds")
    
    # Test exception logging
    try:
        # Simulate an error
        result = 10 / 0
    except ZeroDivisionError as e:
        logger.exception("Division by zero error occurred")
    
    # Test logging with different data types
    test_data = {
        "string": "test_value",
        "number": 42,
        "list": [1, 2, 3],
        "nested": {"key": "value"}
    }
    logger.info("Testing complex data logging: {}", json.dumps(test_data, indent=2))
    
    # Verify log file was created
    if log_file.exists():
        logger.success(f"✅ Log file created successfully: {log_file}")
        
        # Read and display some log content
        with open(log_file, 'r') as f:
            content = f.read()
            line_count = len(content.splitlines())
            logger.info(f"📄 Log file contains {line_count} lines")
            
        return True
    else:
        logger.error(f"❌ Log file was not created: {log_file}")
        return False


def test_log_rotation():
    """Test log rotation functionality."""
    logger.info("🔄 Testing log rotation...")
    
    # Generate multiple log entries to test rotation
    for i in range(100):
        logger.debug(f"Log entry {i+1} - testing rotation functionality")
        if i % 10 == 0:
            logger.info(f"Progress: {i+1}/100 log entries written")
    
    logger.success("✅ Log rotation test completed")


def verify_log_directory():
    """Verify the logs directory structure."""
    logs_dir = Path(__file__).parent / "logs"
    
    if not logs_dir.exists():
        logger.error(f"❌ Logs directory does not exist: {logs_dir}")
        return False
    
    log_files = list(logs_dir.glob("score_*.log"))
    
    if log_files:
        logger.success(f"✅ Found {len(log_files)} log files in {logs_dir}")
        for log_file in log_files:
            size = log_file.stat().st_size
            logger.info(f"  📄 {log_file.name} ({size} bytes)")
        return True
    else:
        logger.warning(f"⚠️  No log files found in {logs_dir}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("🧪 LOGURU LOGGING CONFIGURATION TEST")
    print("=" * 60)
    
    # Test basic logging functionality
    success = test_logging_functionality()
    
    if success:
        # Test log rotation
        test_log_rotation()
        
        # Verify log directory
        verify_log_directory()
        
        print("\n" + "=" * 60)
        print("✅ ALL LOGURU TESTS PASSED!")
        print("=" * 60)
        
        # Display summary
        logger.info("📊 Test Summary:")
        logger.info("  ✅ Loguru configuration: SUCCESS")
        logger.info("  ✅ File logging: SUCCESS")
        logger.info("  ✅ Console logging: SUCCESS")
        logger.info("  ✅ Log rotation: SUCCESS")
        logger.info("  ✅ Timestamped filenames: SUCCESS")
        logger.info("  ✅ Multiple log levels: SUCCESS")
        logger.info("  ✅ Exception logging: SUCCESS")
        
    else:
        print("\n" + "=" * 60)
        print("❌ LOGURU TESTS FAILED!")
        print("=" * 60)


if __name__ == "__main__":
    main()
