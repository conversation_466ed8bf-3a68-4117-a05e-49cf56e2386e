2025-05-29 22:30:05 | INFO     | __main__:setup_logging:60 - Logging configured - Log file: /home/<USER>/repos/autolodge_retrained_deploy/Python/logs/score_2025-05-29_22-30-05.log
2025-05-29 22:30:05 | INFO     | __main__:test_logging_functionality:71 - 🚀 Starting Loguru logging test
2025-05-29 22:30:05 | DEBUG    | __main__:test_logging_functionality:72 - This is a debug message - should appear in file but not console
2025-05-29 22:30:05 | INFO     | __main__:test_logging_functionality:73 - This is an info message - should appear in both console and file
2025-05-29 22:30:05 | WARNING  | __main__:test_logging_functionality:74 - This is a warning message
2025-05-29 22:30:05 | ERROR    | __main__:test_logging_functionality:75 - This is an error message
2025-05-29 22:30:05 | INFO     | __main__:test_logging_functionality:79 - User action logged
2025-05-29 22:30:05 | INFO     | __main__:test_logging_functionality:84 - Processed 150 items in 2.34 seconds
2025-05-29 22:30:05 | ERROR    | __main__:test_logging_functionality:91 - Division by zero error occurred
Traceback (most recent call last):

  File "/home/<USER>/repos/autolodge_retrained_deploy/Python/test_loguru_setup.py", line 189, in <module>
    main()
    └ <function main at 0x7b8ed30acb80>

  File "/home/<USER>/repos/autolodge_retrained_deploy/Python/test_loguru_setup.py", line 159, in main
    success = test_logging_functionality()
              └ <function test_logging_functionality at 0x7b8ed30a73a0>

> File "/home/<USER>/repos/autolodge_retrained_deploy/Python/test_loguru_setup.py", line 89, in test_logging_functionality
    result = 10 / 0

ZeroDivisionError: division by zero
2025-05-29 22:30:05 | INFO     | __main__:test_logging_functionality:100 - Testing complex data logging: {
  "string": "test_value",
  "number": 42,
  "list": [
    1,
    2,
    3
  ],
  "nested": {
    "key": "value"
  }
}
2025-05-29 22:30:05 | SUCCESS  | __main__:test_logging_functionality:104 - ✅ Log file created successfully: /home/<USER>/repos/autolodge_retrained_deploy/Python/logs/score_2025-05-29_22-30-05.log
2025-05-29 22:30:05 | INFO     | __main__:test_logging_functionality:110 - 📄 Log file contains 36 lines
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:120 - 🔄 Testing log rotation...
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 1 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 1/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 2 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 3 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 4 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 5 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 6 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 7 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 8 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 9 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 10 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 11 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 11/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 12 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 13 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 14 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 15 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 16 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 17 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 18 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 19 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 20 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 21 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 21/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 22 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 23 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 24 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 25 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 26 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 27 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 28 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 29 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 30 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 31 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 31/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 32 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 33 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 34 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 35 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 36 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 37 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 38 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 39 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 40 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 41 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 41/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 42 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 43 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 44 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 45 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 46 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 47 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 48 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 49 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 50 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 51 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 51/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 52 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 53 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 54 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 55 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 56 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 57 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 58 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 59 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 60 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 61 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 61/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 62 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 63 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 64 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 65 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 66 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 67 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 68 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 69 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 70 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 71 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 71/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 72 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 73 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 74 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 75 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 76 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 77 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 78 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 79 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 80 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 81 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 81/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 82 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 83 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 84 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 85 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 86 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 87 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 88 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 89 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 90 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 91 - testing rotation functionality
2025-05-29 22:30:05 | INFO     | __main__:test_log_rotation:126 - Progress: 91/100 log entries written
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 92 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 93 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 94 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 95 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 96 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 97 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 98 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 99 - testing rotation functionality
2025-05-29 22:30:05 | DEBUG    | __main__:test_log_rotation:124 - Log entry 100 - testing rotation functionality
2025-05-29 22:30:05 | SUCCESS  | __main__:test_log_rotation:128 - ✅ Log rotation test completed
2025-05-29 22:30:05 | SUCCESS  | __main__:verify_log_directory:142 - ✅ Found 2 log files in /home/<USER>/repos/autolodge_retrained_deploy/Python/logs
2025-05-29 22:30:05 | INFO     | __main__:verify_log_directory:145 -   📄 score_size_rotated_2025-05-29_22-30-05.log (14623 bytes)
2025-05-29 22:30:05 | INFO     | __main__:verify_log_directory:145 -   📄 score_2025-05-29_22-30-05.log (14623 bytes)
2025-05-29 22:30:05 | INFO     | __main__:main:173 - 📊 Test Summary:
2025-05-29 22:30:05 | INFO     | __main__:main:174 -   ✅ Loguru configuration: SUCCESS
2025-05-29 22:30:05 | INFO     | __main__:main:175 -   ✅ File logging: SUCCESS
2025-05-29 22:30:05 | INFO     | __main__:main:176 -   ✅ Console logging: SUCCESS
2025-05-29 22:30:05 | INFO     | __main__:main:177 -   ✅ Log rotation: SUCCESS
2025-05-29 22:30:05 | INFO     | __main__:main:178 -   ✅ Timestamped filenames: SUCCESS
2025-05-29 22:30:05 | INFO     | __main__:main:179 -   ✅ Multiple log levels: SUCCESS
2025-05-29 22:30:05 | INFO     | __main__:main:180 -   ✅ Exception logging: SUCCESS
