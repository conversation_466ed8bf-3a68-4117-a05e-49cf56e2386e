"""
Azure ML SDK v2 Training Script with MLflow Tracking

This script implements a neural network training pipeline using Azure ML SDK v2
with modern MLflow tracking capabilities. It migrates from the legacy v1 SDK
while preserving all existing functionality.

Author: Migrated to Azure ML SDK v2
"""

import argparse
import logging
import os
import sys
import warnings
from typing import Optional, Tuple

import numpy as np

try:
    import mlflow
    import mlflow.tensorflow

    MLFLOW_AVAILABLE = True
except ImportError:
    MLFLOW_AVAILABLE = False
    print('MLflow not available. Logging will be disabled.')

import tensorflow as tf
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential
from tensorflow.keras import optimizers, regularizers
from tensorflow.keras.callbacks import Model<PERSON>heckpoint, TerminateOnNaN
from tensorflow.keras.layers import Dense, Dropout, Input
from tensorflow.keras.models import Model
from utils.prepare_data import download_data, generate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress TensorFlow warnings
warnings.filterwarnings('ignore', category=FutureWarning)
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'


class AzureMLConfig:
    """Configuration class for Azure ML workspace connection."""

    def __init__(self):
        """Initialize configuration from environment variables."""
        self.subscription_id = os.getenv('AZURE_SUBSCRIPTION_ID')
        self.resource_group = os.getenv('AZURE_RESOURCE_GROUP')
        self.workspace_name = os.getenv('AZURE_ML_WORKSPACE_NAME')
        self.experiment_name = os.getenv('AZURE_ML_EXPERIMENT_NAME', 'neural-network-training')

        # Validate required configuration
        if not all([self.subscription_id, self.resource_group, self.workspace_name]):
            logger.warning(
                'Azure ML configuration not found in environment variables. MLflow tracking will work locally only.'
            )

    def get_ml_client(self) -> Optional[MLClient]:
        """Create and return MLClient instance."""
        try:
            if not all([self.subscription_id, self.resource_group, self.workspace_name]):
                return None

            credential = DefaultAzureCredential()
            ml_client = MLClient(
                credential=credential,
                subscription_id=self.subscription_id,
                resource_group_name=self.resource_group,
                workspace_name=self.workspace_name,
            )
            logger.info(f'Connected to Azure ML workspace: {self.workspace_name}')
            return ml_client
        except Exception as e:
            logger.error(f'Failed to connect to Azure ML workspace: {e}')
            return None


def prepare_data(local_dir: str) -> Tuple[tf.sparse.SparseTensor, np.ndarray]:
    """
    Prepare training data by downloading and generating features.

    Args:
        local_dir: Directory to store downloaded data

    Returns:
        Tuple of (X, y) where X is sparse feature tensor and y is one-hot encoded labels
    """
    logger.info(f'Preparing data in directory: {local_dir}')
    try:
        download_data(local_dir=local_dir)
        X, y = generate(local_dir=local_dir)
        logger.info(f'Data prepared successfully. X shape: {X.dense_shape}, y shape: {y.shape}')
        return X, y
    except Exception as e:
        logger.error(f'Failed to prepare data: {e}')
        raise


class LogAcc(tf.keras.callbacks.Callback):
    """
    Custom callback to log accuracy metrics using MLflow.

    This callback replaces the legacy Azure ML Run logging with modern MLflow tracking.
    """

    def __init__(self):
        """Initialize the callback."""
        super().__init__()
        self.epoch_count = 0

    def on_epoch_end(self, epoch: int, logs: Optional[dict] = None) -> None:
        """
        Log metrics at the end of each epoch.

        Args:
            epoch: Current epoch number
            logs: Dictionary containing metric values
        """
        if logs is None:
            logs = {}

        try:
            if MLFLOW_AVAILABLE:
                # Log categorical accuracy
                if 'categorical_accuracy' in logs:
                    mlflow.log_metric('accuracy', logs['categorical_accuracy'], step=epoch)
                    logger.debug(f'Epoch {epoch}: accuracy = {logs["categorical_accuracy"]:.4f}')

                # Log loss
                if 'loss' in logs:
                    mlflow.log_metric('loss', logs['loss'], step=epoch)
                    logger.debug(f'Epoch {epoch}: loss = {logs["loss"]:.4f}')

                # Log validation metrics if available
                if 'val_categorical_accuracy' in logs:
                    mlflow.log_metric('val_accuracy', logs['val_categorical_accuracy'], step=epoch)

                if 'val_loss' in logs:
                    mlflow.log_metric('val_loss', logs['val_loss'], step=epoch)
            else:
                logger.debug(
                    f'Epoch {epoch}: accuracy = {logs.get("categorical_accuracy", "N/A"):.4f}, loss = {logs.get("loss", "N/A"):.4f}'
                )

        except Exception as e:
            logger.error(f'Failed to log metrics for epoch {epoch}: {e}')

        self.epoch_count += 1


def create_model(
    input_shape: int,
    output_shape: int,
    num_nodes: int,
    drop_out_rate: float,
    regularise_factor: float,
    learning_rate: float,
) -> Model:
    """
    Create and compile the neural network model.

    Args:
        input_shape: Number of input features
        output_shape: Number of output classes
        num_nodes: Number of nodes in hidden layer
        drop_out_rate: Dropout rate for regularization
        regularise_factor: L2 regularization factor
        learning_rate: Learning rate for optimizer

    Returns:
        Compiled Keras model
    """
    logger.info('Creating neural network model')

    # Define model architecture
    input_tensor = Input(shape=input_shape, sparse=True, name='input')
    hidden_tensor = Dense(num_nodes, activation='relu', name='hidden')(input_tensor)
    dropout_tensor = Dropout(drop_out_rate, name='dropout')(hidden_tensor)
    output_tensor = Dense(
        output_shape,
        kernel_regularizer=regularizers.l2(regularise_factor),
        activation='softmax',
        name='output',
    )(dropout_tensor)

    # Create model
    model = Model(inputs=input_tensor, outputs=output_tensor, name='neural_network')

    # Compile model
    optimizer_instance = optimizers.Adam(learning_rate=learning_rate)
    model.compile(
        optimizer=optimizer_instance,
        loss='categorical_crossentropy',
        metrics=['categorical_accuracy'],
    )

    logger.info(f'Model created with {model.count_params()} parameters')
    return model


def fine_tune_model(
    X: tf.sparse.SparseTensor,
    y: np.ndarray,
    num_epochs: int,
    drop_out_rate: float,
    num_nodes: int,
    regularise_factor: float,
    learning_rate: float,
) -> None:
    """
    Train the neural network model with MLflow tracking.

    Args:
        X: Sparse input features
        y: One-hot encoded target labels
        num_epochs: Number of training epochs
        drop_out_rate: Dropout rate for regularization
        num_nodes: Number of nodes in hidden layer
        regularise_factor: L2 regularization factor
        learning_rate: Learning rate for optimizer
    """
    logger.info('Starting model training')

    try:
        # Log hyperparameters
        hyperparameters = {
            'num_epochs': int(num_epochs),
            'learning_rate': float(learning_rate),
            'drop_out_rate': float(drop_out_rate),
            'regularisation_factor': float(regularise_factor),
            'num_nodes': int(num_nodes),
            'batch_size': 256,
            'optimizer': 'Adam',
            'loss_function': 'categorical_crossentropy',
        }

        if MLFLOW_AVAILABLE:
            mlflow.log_params(hyperparameters)
            logger.info(f'Logged hyperparameters: {hyperparameters}')
        else:
            logger.info(f'Hyperparameters: {hyperparameters}')

        # Create model
        # Get input shape from sparse tensor
        dense_shape = X.dense_shape
        input_shape = dense_shape[1]
        model = create_model(
            input_shape=input_shape,
            output_shape=y.shape[1],
            num_nodes=num_nodes,
            drop_out_rate=drop_out_rate,
            regularise_factor=regularise_factor,
            learning_rate=learning_rate,
        )

        # Log model summary
        model_summary = []
        model.summary(print_fn=lambda x: model_summary.append(x))
        if MLFLOW_AVAILABLE:
            mlflow.log_text('\n'.join(model_summary), 'model_summary.txt')
        else:
            logger.info('Model summary:\n' + '\n'.join(model_summary))

        # Create outputs directory for checkpoints
        outputs_dir = 'outputs'
        os.makedirs(outputs_dir, exist_ok=True)

        # Define callbacks
        callbacks = [
            TerminateOnNaN(),
            ModelCheckpoint(
                filepath=os.path.join(outputs_dir, 'epoch_{epoch:02d}.h5'),
                save_freq='epoch',
                save_best_only=False,
                save_weights_only=False,
                verbose=1,
            ),
            LogAcc(),
        ]

        # Train model
        logger.info(f'Training model for {num_epochs} epochs')
        history = model.fit(x=X, y=y, epochs=num_epochs, batch_size=256, callbacks=callbacks, verbose=1)

        # Log final metrics
        final_metrics = {
            'final_loss': float(history.history['loss'][-1]),
            'final_accuracy': float(history.history['categorical_accuracy'][-1]),
            'total_epochs_completed': len(history.history['loss']),
        }

        if MLFLOW_AVAILABLE:
            mlflow.log_metrics(final_metrics)

            # Save final model
            final_model_path = os.path.join(outputs_dir, 'final_model.h5')
            model.save(final_model_path)
            mlflow.log_artifact(final_model_path, 'model')

            # Log model with MLflow
            mlflow.tensorflow.log_model(
                model, 'keras_model', registered_model_name=f'neural_network_{mlflow.active_run().info.run_id}'
            )
        else:
            logger.info(f'Final metrics: {final_metrics}')
            # Save final model
            final_model_path = os.path.join(outputs_dir, 'final_model.h5')
            model.save(final_model_path)
            logger.info(f'Model saved to: {final_model_path}')

        logger.info('Model training completed successfully')

    except Exception as e:
        logger.error(f'Training failed: {e}')
        if MLFLOW_AVAILABLE:
            mlflow.log_param('training_status', 'failed')
            mlflow.log_param('error_message', str(e))
        raise


def main() -> None:
    """Main training function with Azure ML SDK v2 and MLflow integration."""
    logger.info('Starting neural network training with Azure ML SDK v2')

    try:
        # Initialize Azure ML configuration
        config = AzureMLConfig()
        ml_client = config.get_ml_client()

        # Set MLflow tracking URI if Azure ML is available
        if ml_client and MLFLOW_AVAILABLE:
            try:
                # Set MLflow tracking to Azure ML workspace
                workspace = ml_client.workspaces.get(config.workspace_name)
                if hasattr(workspace, 'mlflow_tracking_uri'):
                    mlflow.set_tracking_uri(workspace.mlflow_tracking_uri)
                    logger.info('MLflow tracking configured for Azure ML workspace')
                else:
                    logger.warning('MLflow tracking URI not available in workspace')
            except Exception as e:
                logger.warning(f'Failed to set Azure ML tracking URI: {e}')

        # Parse command line arguments
        parser = argparse.ArgumentParser(description='Train neural network with Azure ML SDK v2')
        parser.add_argument('--num_epochs', type=int, default=10, help='Number of epochs to train')
        parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate for optimizer')
        parser.add_argument('--drop_out_rate', type=float, default=0.2, help='Dropout rate for regularization')
        parser.add_argument('--num_nodes', type=int, default=512, help='Number of nodes in hidden layer')
        parser.add_argument('--regularise_factor', type=float, default=1e-4, help='L2 regularization factor')
        parser.add_argument('--data_dir', type=str, default='./data/interim', help='Directory for training data')

        args = parser.parse_args()

        # Validate arguments
        if args.num_epochs <= 0:
            raise ValueError('num_epochs must be positive')
        if not (0 <= args.drop_out_rate <= 1):
            raise ValueError('drop_out_rate must be between 0 and 1')
        if args.learning_rate <= 0:
            raise ValueError('learning_rate must be positive')

        # Prepare data
        X, y = prepare_data(local_dir=args.data_dir)

        # Start MLflow run or train without MLflow
        if MLFLOW_AVAILABLE:
            with mlflow.start_run(run_name=f'neural_network_training_{args.num_epochs}epochs') as run:
                logger.info(f'Started MLflow run: {run.info.run_id}')

                # Set experiment
                mlflow.set_experiment(config.experiment_name)

                # Log system information
                mlflow.log_param('tensorflow_version', tf.__version__)
                mlflow.log_param('python_version', sys.version)
                mlflow.log_param('data_directory', args.data_dir)

                # Train model
                fine_tune_model(
                    X=X,
                    y=y,
                    num_epochs=args.num_epochs,
                    drop_out_rate=args.drop_out_rate,
                    num_nodes=args.num_nodes,
                    regularise_factor=args.regularise_factor,
                    learning_rate=args.learning_rate,
                )

                logger.info(f'Training completed. MLflow run ID: {run.info.run_id}')
        else:
            logger.info('Training without MLflow tracking')
            # Train model without MLflow
            fine_tune_model(
                X=X,
                y=y,
                num_epochs=args.num_epochs,
                drop_out_rate=args.drop_out_rate,
                num_nodes=args.num_nodes,
                regularise_factor=args.regularise_factor,
                learning_rate=args.learning_rate,
            )
            logger.info('Training completed successfully')

    except Exception as e:
        logger.error(f'Training pipeline failed: {e}')
        sys.exit(1)


if __name__ == '__main__':
    main()
