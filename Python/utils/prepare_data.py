import os
import pickle

import numpy as np
import pandas as pd
import scipy
import tensorflow as tf
from tensorflow import keras
from tqdm import tqdm

from .util import AzureStorageUtil


def download_data(local_dir: str):
    if not os.path.exists(local_dir):
        os.makedirs(local_dir, exist_ok=True)

    AzureStorageUtil.download_all_from_cloud(container_name='online-training-test', local_dir=local_dir)


def generate(local_dir: str):
    if not os.path.exists(local_dir):
        os.mkdir(local_dir)

    tqdm.pandas()

    # tokenizer
    pkl_file = open(os.path.join(local_dir, 'tk.pkl'), 'rb')
    tk = pickle.load(pkl_file)
    pkl_file.close()

    # label_encoder
    pkl_file = open(os.path.join(local_dir, 'le.pkl'), 'rb')
    le = pickle.load(pkl_file)
    pkl_file.close()

    # x
    coo = scipy.sparse.load_npz(
        os.path.join(
            local_dir,
            'tokenized_raw_t.npz',
        )
    )
    x = tf.sparse.SparseTensor(
        indices=list(zip(coo.row, coo.col)),
        values=coo.data,
        dense_shape=coo.get_shape(),
    )

    # y
    token_names = pd.read_parquet(os.path.join(local_dir, 'y.parquet')).Token_Name
    n_classes = len(np.unique(token_names))
    y = keras.utils.to_categorical(le.transform(token_names), num_classes=n_classes)

    return x, y


if __name__ == '__main__':
    download_data('../../data/interim')
    generate(local_dir='../../data/interim/')
