import logging
import os
import uuid
from typing import Optional
from zipfile import ZipFile

from azure.storage.blob import BlobServiceClient

params = {}
params['version'] = 'new'
params['tk_mode'] = 'binary'
params['mc'] = 20
params['include_price'] = False
params['stemming'] = False
params['sample'] = False
params['n_tokens'] = 10000
params['partition'] = 12


class Util:
    NAMESPACE_RAWDATA = uuid.UUID('0' * 32)
    NAMESPACE_DATA = uuid.UUID('1' * 32)
    NAMESPACE_DISTINCTDATA = uuid.UUID('2' * 32)
    NAMESPACE_UTIL = uuid.UUID('3' * 32)
    NAMESPACE_MODEL = uuid.UUID('4' * 32)
    NAMESPACE_RESULT = uuid.UUID('5' * 32)
    NAMESPACE_RUN = uuid.UUID('6' * 32)
    NAMESPACE_RULE = uuid.UUID('7' * 32)

    @staticmethod
    def get_UUID(params: dict, type: str, **kwargs):
        """
        Generate a unique id for the data depending on the parameters
        Args:
            params: Config parameters
            type:
                'x': dataset
                'y': y labels
                'r': raw data
                'p': price data
                't': tokenizer
                'e': label encoder
                's': standard scaler
                'd': distinct dataset
                'dp': distinct t-tstarc pairs
                'm': model
                'i': interim results
                'r': running
                'br': business rules
                'mt': mapping table
                'ad': acceptable differences
            **kwargs: if type is 'x', 'y' or 'p' there should be another parameter 'partition' indicating training,
                        validating or testing set it is

        Returns:

        """
        name = '$'.join(str(_) for _ in params.values()) + '$' + type
        for k, v in kwargs.items():
            name = name + '$' + str(v)
        if type in ['x', 'y', 'p']:
            return uuid.uuid3(Util.NAMESPACE_DATA, name).hex
        elif type == 'r':
            return uuid.uuid3(Util.NAMESPACE_RAWDATA, name).hex
        elif type in ['t', 'e', 's']:
            return uuid.uuid3(Util.NAMESPACE_UTIL, name).hex
        elif type in ['d', 'dp']:
            return uuid.uuid3(Util.NAMESPACE_DISTINCTDATA, name).hex
        elif type == 'm':
            return uuid.uuid3(Util.NAMESPACE_MODEL, name).hex
        elif type == 'i':
            return uuid.uuid3(Util.NAMESPACE_RESULT, name).hex
        elif type == 'r':
            return uuid.uuid3(Util.NAMESPACE_RUN, name).hex
        elif type in ['br', 'mt', 'ad']:
            return uuid.uuid3(Util.NAMESPACE_RULE, name).hex


class AzureStorageUtil:
    """
    Utilities used to access the Azure Blob Storage with secure authentication.

    This class provides secure access to Azure Blob Storage using environment variables
    for authentication. It supports two authentication methods:
    1. Connection string via AZURE_STORAGE_CONNECTION_STRING
    2. Account name and key via AZURE_STORAGE_ACCOUNT_NAME and AZURE_STORAGE_ACCOUNT_KEY
    """

    _bsc = None
    _logger = logging.getLogger(__name__)

    @classmethod
    def _get_blob_service_client(cls) -> BlobServiceClient:
        """
        Get a secure BlobServiceClient instance using environment variables.

        Returns:
            BlobServiceClient: Authenticated Azure Blob Storage client

        Raises:
            ValueError: If required environment variables are not set
            Exception: If authentication fails
        """
        if cls._bsc is not None:
            return cls._bsc

        try:
            # Method 1: Try connection string first (recommended)
            conn_str = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
            if conn_str:
                cls._logger.info('Authenticating with Azure Storage using connection string')
                cls._bsc = BlobServiceClient.from_connection_string(conn_str)
                cls._logger.info('Successfully authenticated with Azure Storage')
                return cls._bsc

            # Method 2: Try account name and key
            account_name = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
            account_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')

            if account_name and account_key:
                cls._logger.info('Authenticating with Azure Storage using account name and key')
                account_url = f'https://{account_name}.blob.core.windows.net'
                cls._bsc = BlobServiceClient(account_url=account_url, credential=account_key)
                cls._logger.info('Successfully authenticated with Azure Storage')
                return cls._bsc

            # No valid credentials found
            raise ValueError(
                'Azure Storage credentials not found. Please set one of the following:\n'
                '1. AZURE_STORAGE_CONNECTION_STRING environment variable, or\n'
                '2. Both AZURE_STORAGE_ACCOUNT_NAME and AZURE_STORAGE_ACCOUNT_KEY environment variables'
            )

        except Exception as e:
            cls._logger.error(f'Failed to authenticate with Azure Storage: {str(e)}')
            raise

    @classmethod
    def get_default_client(cls) -> BlobServiceClient:
        """
        Get the default BlobServiceClient for backward compatibility.

        Returns:
            BlobServiceClient: Authenticated Azure Blob Storage client
        """
        return cls._get_blob_service_client()

    # Maintain backward compatibility with the original 'bsc' class variable
    @classmethod
    def get_bsc(cls) -> BlobServiceClient:
        """Backward compatibility method for the original 'bsc' class variable."""
        return cls._get_blob_service_client()

    @property
    def bsc(self) -> BlobServiceClient:
        """Backward compatibility property for the original 'bsc' class variable."""
        return self._get_blob_service_client()

    @classmethod
    def download_all_from_cloud(
        cls,
        container_name: str,
        local_dir: str,
        blob_service_client: Optional[BlobServiceClient] = None,
    ) -> None:
        """
        Download all blobs from Azure blob storage container to local directory.

        Args:
            container_name (str): Name of the Azure blob storage container
            local_dir (str): Local directory path to download files to
            blob_service_client (BlobServiceClient, optional): Custom blob service client.
                If None, uses the default secure client from environment variables.

        Returns:
            None

        Raises:
            ValueError: If container_name or local_dir is invalid
            Exception: If download operation fails
        """
        if not container_name:
            raise ValueError('Container name cannot be empty')
        if not local_dir:
            raise ValueError('Local directory path cannot be empty')

        # Use provided client or get default secure client
        if blob_service_client is None:
            blob_service_client = cls._get_blob_service_client()

        try:
            cls._logger.info(f"Starting download from container '{container_name}' to '{local_dir}'")
            os.makedirs(local_dir, exist_ok=True)

            container_client = blob_service_client.get_container_client(container=container_name)
            blob_list = container_client.list_blobs()

            download_count = 0
            for blob in blob_list:
                blob_client = container_client.get_blob_client(blob=blob.name)
                cls._logger.info(f'Downloading: {blob.name}')
                print(f'\t Downloading: {blob.name}')  # Maintain original print for compatibility
                download_file_path = os.path.join(local_dir, blob.name)

                with open(download_file_path, 'wb') as download_file:
                    download_file.write(blob_client.download_blob().readall())
                download_count += 1

            cls._logger.info(f"Successfully downloaded {download_count} files from container '{container_name}'")

        except Exception as e:
            cls._logger.error(f"Failed to download from container '{container_name}': {str(e)}")
            raise

    @classmethod
    def download_utils_from_cloud(
        cls,
        container_name: str,
        local_dir: str,
        blob_service_client: Optional[BlobServiceClient] = None,
    ) -> None:
        """
        Download specific utility files from Azure blob storage container.

        Downloads only the required utility files: le.pkl, tk.pkl, abbr.csv, corpus.pkl

        Args:
            container_name (str): Name of the Azure blob storage container
            local_dir (str): Local directory path to download files to
            blob_service_client (BlobServiceClient, optional): Custom blob service client.
                If None, uses the default secure client from environment variables.

        Returns:
            None

        Raises:
            ValueError: If container_name or local_dir is invalid
            Exception: If download operation fails
        """
        if not container_name:
            raise ValueError('Container name cannot be empty')
        if not local_dir:
            raise ValueError('Local directory path cannot be empty')

        # Use provided client or get default secure client
        if blob_service_client is None:
            blob_service_client = cls._get_blob_service_client()

        try:
            cls._logger.info(f"Starting utility download from container '{container_name}' to '{local_dir}'")
            os.makedirs(local_dir, exist_ok=True)

            container_client = blob_service_client.get_container_client(container=container_name)
            util_list = ['le.pkl', 'tk.pkl', 'abbr.csv', 'corpus.pkl']

            blob_list = container_client.list_blobs()
            download_count = 0
            for blob in blob_list:
                if blob.name in util_list:
                    blob_client = container_client.get_blob_client(blob=blob.name)
                    cls._logger.info(f'Downloading utility: {blob.name}')
                    print(f'\t Downloading: {blob.name}')  # Maintain original print for compatibility
                    download_file_path = os.path.join(local_dir, blob.name)

                    with open(download_file_path, 'wb') as download_file:
                        download_file.write(blob_client.download_blob().readall())
                    download_count += 1

            cls._logger.info(
                f"Successfully downloaded {download_count} utility files from container '{container_name}'"
            )

        except Exception as e:
            cls._logger.error(f"Failed to download utilities from container '{container_name}': {str(e)}")
            raise

    @staticmethod
    def unzip_all(data_path: str) -> None:
        """
        Unzip all the zip files in the specified directory.

        Args:
            data_path (str): Path to the directory containing zip files to extract

        Returns:
            None

        Raises:
            ValueError: If data_path is invalid
            Exception: If extraction fails
        """
        if not data_path:
            raise ValueError('Data path cannot be empty')
        if not os.path.exists(data_path):
            raise ValueError(f'Data path does not exist: {data_path}')

        logger = logging.getLogger(__name__)

        try:
            logger.info(f'Starting zip extraction in directory: {data_path}')
            extraction_count = 0

            for root, dirs, files in os.walk(data_path):
                for file in files:
                    if file.endswith('.zip'):
                        zip_path = os.path.join(root, file)
                        logger.info(f'Extracting: {zip_path}')

                        with ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall(data_path)
                        extraction_count += 1

            logger.info(f'Successfully extracted {extraction_count} zip files')

        except Exception as e:
            logger.error(f"Failed to extract zip files in '{data_path}': {str(e)}")
            raise


if __name__ == '__main__':
    pass
