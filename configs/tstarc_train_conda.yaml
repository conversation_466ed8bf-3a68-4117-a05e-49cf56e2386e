#name: treatment_model_deployment_new
channels:
  - conda-forge
  - Microsoft
  - defaults
dependencies:
  # - anyio=3.5.0=py39haa95532_0
  # - argon2-cffi=21.3.0=pyhd3eb1b0_0
  # - argon2-cffi-bindings=21.2.0=py39h2bbff1b_0
  # - astroid=2.9.0=py39haa95532_0
  # - asttokens=2.0.5=pyhd3eb1b0_0
  # - attrs=21.4.0=pyhd3eb1b0_0
  # - autopep8=1.6.0=pyhd3eb1b0_1
  # - babel=2.9.1=pyhd3eb1b0_0
  # - backcall=0.2.0=pyhd3eb1b0_0
  # - beautifulsoup4=4.11.1=py39haa95532_0
  # - blas=1.0=mkl
  # - bleach=4.1.0=pyhd3eb1b0_0
  # - bottleneck=1.3.4=py39h080aedc_0
  # - brotlipy=0.7.0=py39h2bbff1b_1003
  # - ca-certificates=2022.4.26=haa95532_0
  # - catalogue=2.0.6=py39hcbf5309_2
  # - certifi=2022.5.18.1=py39haa95532_0
  # - cffi=1.15.0=py39h2bbff1b_1
  # - click=8.0.4=py39haa95532_0
  # - colorama=0.4.4=pyhd3eb1b0_0
  # - cramjam=2.5.0=py39h424382f_0
  - cudatoolkit=11.6.0
  - gxx_linux-64
#  - cupy=10.3.1=py39hc474d4a_0
  # - cymem=2.0.6=py39h415ef7b_3
#  - cython-blis=0.7.7=py39h5d4886f_0
  # - dataclasses=0.8=pyh6d0b6a4_7
  # - debugpy=1.5.1=py39hd77b12b_0
  # - decorator=5.1.1=pyhd3eb1b0_0
  # - defusedxml=0.7.1=pyhd3eb1b0_0
  # - entrypoints=0.4=py39haa95532_0
  # - et_xmlfile=1.1.0=py39haa95532_0
  # - executing=0.8.3=pyhd3eb1b0_0
  - fastparquet=0.8.0
  # - fastrlock=0.8=py39h415ef7b_2
  # - flake8=4.0.1=pyhd3eb1b0_1
  # - fsspec=2022.2.0=pyhd3eb1b0_0
  # - icu=58.2=ha925a31_3
  # - idna=3.3=pyhd3eb1b0_0
  # - importlib-metadata=4.11.3=py39haa95532_0
  # - intel-openmp=2021.4.0=haa95532_3556
  # - ipykernel=6.9.1=py39haa95532_0
  # - ipython=8.2.0=py39haa95532_0
  # - ipython_genutils=0.2.0=pyhd3eb1b0_1
  # - ipywidgets=7.6.5=pyhd3eb1b0_1
  # - isort=5.10.1=pyhd8ed1ab_0
  # - jinja2=3.0.3=pyhd3eb1b0_0
  # - jlab-enhanced-cell-toolbar=3.4.2=pyhd8ed1ab_0
  # - joblib=1.1.0=pyhd3eb1b0_0
  # - jpeg=9d=h2bbff1b_0
  # - json5=0.9.6=pyhd3eb1b0_0
  # - jsonschema=4.4.0=py39haa95532_0
  # - jupyter=1.0.0=py39haa95532_7
  # - jupyter-lsp=1.5.1=pyhd8ed1ab_0
  # - jupyter_client=7.1.2=pyhd3eb1b0_0
  # - jupyter_console=6.4.3=pyhd3eb1b0_0
  # - jupyter_core=4.9.2=py39haa95532_0
  # - jupyter_server=1.13.5=pyhd3eb1b0_0
  # - jupyterlab=3.3.2=pyhd3eb1b0_0
  # - jupyterlab-lsp=3.10.1=pyhd8ed1ab_0
  # - jupyterlab_execute_time=2.1.0=pyhd8ed1ab_0
  # - jupyterlab_pygments=0.1.2=py_0
  # - jupyterlab_server=2.12.0=py39haa95532_0
  # - jupyterlab_widgets=1.0.0=pyhd3eb1b0_1
  # - langcodes=3.3.0=pyhd3eb1b0_0
  # - lazy-object-proxy=1.7.1=py39hb82d6ee_0
  # - libpng=1.6.37=h2a8f88b_0
  # - m2w64-gcc-libgfortran=5.3.0=6
  # - m2w64-gcc-libs=5.3.0=7
  # - m2w64-gcc-libs-core=5.3.0=7
  # - m2w64-gmp=6.1.0=2
  # - m2w64-libwinpthread-git=5.0.0.4634.697f757=2
  # - markupsafe=2.0.1=py39h2bbff1b_0
  # - matplotlib-inline=0.1.2=pyhd3eb1b0_2
  # - mccabe=0.6.1=py39haa95532_1
  # - mistune=0.8.4=py39h2bbff1b_1000
  # - mkl=2021.4.0=haa95532_640
  # - mkl-service=2.4.0=py39h2bbff1b_0
  # - mkl_fft=1.3.1=py39h277e83a_0
  # - mkl_random=1.2.2=py39hf11a4ad_0
  # - msys2-conda-epoch=20160418=1
  # - murmurhash=1.0.6=py39h415ef7b_3
  # - nbclassic=0.3.5=pyhd3eb1b0_0
  # - nbclient=0.5.13=py39haa95532_0
  # - nbconvert=6.4.4=py39haa95532_0
  # - nbformat=5.3.0=py39haa95532_0
  # - nest-asyncio=1.5.5=py39haa95532_0
  # - notebook=6.4.8=py39haa95532_0
  # - numexpr=2.8.1=py39hb80d3ca_0
  # - numpy=1.21.5=py39h7a0a035_1
  # - numpy-base=1.21.5=py39hca35cd5_1
  # - openpyxl=3.0.9=pyhd3eb1b0_0
  # - openssl=1.1.1o=h2bbff1b_0
  # - packaging=21.3=pyhd3eb1b0_0
  - pandas=1.4.1
  # - pandocfilters=1.5.0=pyhd3eb1b0_0
  # - pathy=0.6.1=pyhd8ed1ab_0
  # - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pip=21.2.4
  # - pluggy=1.0.0=py39hcbf5309_2
  # - preshed=3.0.6=py39h415ef7b_2
  # - prometheus_client=0.13.1=pyhd3eb1b0_0
  # - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  # - prompt_toolkit=3.0.20=hd3eb1b0_0
  # - pure_eval=0.2.2=pyhd3eb1b0_0
  # - pycodestyle=2.8.0=pyhd3eb1b0_0
  # - pycparser=2.21=pyhd3eb1b0_0
  # - pydantic=1.8.2=py39hb82d6ee_2
  # - pydocstyle=6.1.1=pyhd3eb1b0_0
  # - pyflakes=2.4.0=pyhd3eb1b0_0
  # - pygments=2.11.2=pyhd3eb1b0_0
  # - pylint=2.12.2=py39haa95532_1
  # - pyparsing=3.0.4=pyhd3eb1b0_0
  # - pyqt=5.9.2=py39hd77b12b_6
  # - pyrsistent=0.18.0=py39h196d8e1_0
  # - pysocks=1.7.1=py39haa95532_0
  - python=3.9
  # - python-dateutil=2.8.2=pyhd3eb1b0_0
  # - python-fastjsonschema=2.15.1=pyhd3eb1b0_0
  # - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  # - python-lsp-server=1.3.3=pyhd3eb1b0_0
  # - python_abi=3.9=2_cp39
  # - pytz=2021.3=pyhd3eb1b0_0
  # - pyzmq=22.3.0=py39hd77b12b_2
  # - qt=5.9.7=vc14h73c81de_0
  # - qtconsole=5.3.0=pyhd3eb1b0_0
  # - qtpy=2.0.1=pyhd3eb1b0_0
  - regex
  # - requests=2.27.1=pyhd3eb1b0_0
  # - rope=0.23.0=pyhd8ed1ab_0
  # - send2trash=1.8.0=pyhd3eb1b0_1
  # - setuptools=61.2.0=py39haa95532_0
  # - shellingham=1.4.0=pyh44b312d_0
  # - sip=4.19.13=py39hd77b12b_0
  # - six=1.16.0=pyhd3eb1b0_1
  # - smart_open=5.2.1=pyhd8ed1ab_0
  # - sniffio=1.2.0=py39haa95532_1
  # - snowballstemmer=2.2.0=pyhd3eb1b0_0
  # - soupsieve=2.3.1=pyhd3eb1b0_0
  # - spacy=3.2.4=py39hefe7e4c_0
  # - spacy-legacy=3.0.9=pyhd8ed1ab_0
  # - spacy-loggers=1.0.2=pyhd8ed1ab_0
  # - sqlite=3.38.2=h2bbff1b_0
  # - srsly=2.4.2=py39h415ef7b_1
  # - stack_data=0.2.0=pyhd3eb1b0_0
  # - terminado=0.13.1=py39haa95532_0
  # - testpath=0.5.0=pyhd3eb1b0_0
  # - thinc=8.0.15=py39hefe7e4c_0
  # - toml=0.10.2=pyhd3eb1b0_0
  # - tornado=6.1=py39h2bbff1b_0
  # - traitlets=5.1.1=pyhd3eb1b0_0
  # - typer=0.4.1=pyhd8ed1ab_0
  # - typing-extensions=4.1.1=hd3eb1b0_0
  # - typing_extensions=4.1.1=pyh06a4308_0
  # - tzdata=2022a=hda174b7_0
  # - ujson=5.1.0=py39hd77b12b_0
  # - vc=14.2=h21ff451_1
  # - vs2015_runtime=14.27.29016=h5e58377_2
  # - wasabi=0.9.1=pyhd8ed1ab_0
  # - wcwidth=0.2.5=pyhd3eb1b0_0
  # - webencodings=0.5.1=py39haa95532_1
  # - wheel=0.37.1=pyhd3eb1b0_0
  # - widgetsnbextension=3.5.2=py39haa95532_0
  # - win_inet_pton=1.1.0=py39haa95532_0
  # - wincertstore=0.2=py39haa95532_2
  # - winpty=0.4.3=4
  # - wrapt=1.13.3=py39h2bbff1b_2
  # - yapf=0.32.0=pyhd8ed1ab_0
  # - zipp=3.7.0=pyhd3eb1b0_0
  - zlib=1.2.12
  - pip:
  #   - absl-py==1.0.0
  #   - adal==1.2.7
  #   - aiohttp==3.8.1
  #   - aiosignal==1.2.0
  #   - alembic==1.7.6
  #   - applicationinsights==0.11.10
  #   - argcomplete==1.12.3
  #   - arrow==1.2.2
  #   - astunparse==1.6.3
  #   - async-timeout==4.0.2
    - azure-ai-ml
    - azure-common
    - azure-core
    - azure-graphrbac
    - azure-identity
    - azure-mgmt-authorization
    - azure-mgmt-containerregistry
    - azure-mgmt-core
    - azure-mgmt-keyvault
    - azure-mgmt-resource
    - azure-mgmt-storage
    - azure-storage-blob
    - azure-storage-file-share
    - azureml-core
    - azureml-mlflow
  #   - backports-tempfile==1.0
  #   - backports-weakref==1.0.post1
  #   - bcrypt==3.2.0
  #   - binaryornot==0.4.4
  #   - black==22.1.0
  #   - cachetools==5.0.0
  #   - chardet==4.0.0
  #   - charset-normalizer==2.0.12
  #   - cloudpickle==2.0.0
  #   - commonmark==0.9.1
  #   - contextlib2==21.6.0
  #   - cookiecutter==1.7.3
  #   - cryptography==36.0.1
  #   - cycler==0.11.0
  #   - cython==0.29.23
  #   - databricks-cli==0.16.4
  #   - docker==5.0.3
  #   - docstring-to-markdown==0.10
  #   - editdistpy==0.1.3
  #   - en-core-web-sm==3.2.0
  #   - flask==2.0.3
  #   - flatbuffers==2.0
  #   - fonttools==4.29.1
  #   - frozenlist==1.3.0
  #   - gast==0.5.3
  #   - gensim==4.1.2
  #   - gitdb==4.0.9
  #   - gitpython==3.1.27
  #   - google-auth==2.6.0
  #   - google-auth-oauthlib==0.4.6
  #   - google-pasta==0.2.0
  #   - greenlet==1.1.2
  #   - grpcio==1.44.0
  #   - h5py==3.6.0
  #   - humanfriendly==10.0
  #   - isodate==0.6.1
  #   - itsdangerous==2.1.0
  #   - jedi==0.17.2
  #   - jeepney==0.7.1
  #   - jinja2-time==0.2.0
  #   - jmespath==0.10.0
  #   - jsonpickle==2.1.0
  #   - jupyterlab-system-monitor==0.8.0
  #   - jupyterlab-topbar==0.6.1
  #   - keras==2.8.0
  #   - keras-preprocessing==1.1.2
  #   - kiwisolver==1.3.2
  #   - knack==0.8.2
  #   - libclang==13.0.0
  #   - loguru==0.6.0
  #   - mako==1.1.6
  #   - markdown==3.3.6
  #   - marshmallow==3.15.0
#    - matplotlib==3.5.1
  #   - mlflow==1.23.1
  #   - mlflow-skinny==1.23.1
  #   - msal==1.17.0
  #   - msal-extensions==0.3.1
  #   - msrest==0.6.21
  #   - msrestazure==0.6.4
  #   - multidict==6.0.2
  #   - mypy-extensions==0.4.3
  #   - ndg-httpsclient==0.5.1
    - nltk==3.7
  #   - oauthlib==3.2.0
  #   - opt-einsum==3.3.0
  #   - paramiko==2.9.2
  #   - parso==0.7.1
  #   - pathspec==0.9.0
  #   - pillow==9.0.1
  #   - pkginfo==1.8.2
  #   - platformdirs==2.5.1
  #   - portalocker==2.4.0
  #   - poyo==0.5.0
  #   - prometheus-flask-exporter==0.18.7
    - protobuf==3.19.4
  #   - psutil==5.9.0
  #   - pyasn1==0.4.8
  #   - pyasn1-modules==0.2.8
  #   - pydash==4.9.0
  #   - pydeprecate==0.3.2
  #   - pyjwt==2.3.0
  #   - pynacl==1.5.0
#    - pyodbc==4.0.32
#    - pyopenssl==21.0.0
  #   - pypiwin32==223
  #   - pypushdeer==0.0.3
  #   - pyreadline3==3.4.1
  #   - python-jsonrpc-server==0.4.0
  #   - python-language-server==0.36.2+49.g3536061
  #   - python-slugify==6.1.1
  #   - pytorch-lightning==1.6.3
  #   - pywin32==227
  #   - pywinpty==1.1.6
  #   - pyyaml==6.0
  #   - querystring-parser==1.2.4
  #   - regex==2022.3.2
  #   - requests-oauthlib==1.3.1
  #   - rich==11.2.0
  #   - rsa==4.8
    - scikit-learn==1.0.2
    - scipy==1.8.0
  #   - secretstorage==3.3.1
  #   - smmap==5.0.0
    - sqlalchemy==1.4.31
  #   - sqlparse==0.4.2
  #   - symspellpy==6.7.6
  #   - tabulate==0.8.9
  #   - tensorboard==2.8.0
  #   - tensorboard-data-server==0.6.1
  #   - tensorboard-plugin-wit==1.8.1
    - tensorflow==2.8.0
#    - tensorflow-io-gcs-filesystem==0.24.0
  #   - termcolor==1.1.0
  #   - text-unidecode==1.3
  #   - tf-estimator-nightly==2.8.0.dev2021122109
  #   - threadpoolctl==3.1.0
  #   - tomli==2.0.1
  #   - torch==1.11.0
  #   - torchmetrics==0.8.1
    - tqdm==4.63.0
#    - urllib3==1.26.7
  #   - waitress==2.0.0
  #   - websocket-client==1.2.3
  #   - werkzeug==2.0.3
  #   - win32-setctime==1.1.0
  #   - yarl==1.7.2
prefix: F:\Anaconda3\envs\treatment_model_deployment_new
