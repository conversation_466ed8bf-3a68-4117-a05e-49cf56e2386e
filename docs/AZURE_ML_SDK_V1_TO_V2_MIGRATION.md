# Azure ML SDK v1 to v2 Migration Guide

## Overview

This document provides a comprehensive guide for migrating Azure ML SDK v1 deployment scripts to v2, specifically documenting the migration of `submit_deploy_v1.py` to `submit_deploy_v1_migrated.py`.

## Migration Summary

The migration successfully transforms the original v1 deployment script to use modern Azure ML SDK v2 patterns while maintaining the same deployment functionality and workflow.

### Key Migration Changes

#### 1. SDK Imports Migration

**v1 (Original):**
```python
from azureml.core import Environment, Workspace
from azureml.core.authentication import InteractiveLoginAuthentication
from azureml.core.compute import AksCompute
from azureml.core.model import InferenceConfig, Model
from azureml.core.webservice import AksWebservice
from azureml.exceptions import WebserviceException
```

**v2 (Migrated):**
```python
from azure.ai.ml import MLClient
from azure.ai.ml.constants import AssetTypes
from azure.ai.ml.entities import CodeConfiguration, Environment, ManagedOnlineDeployment, ManagedOnlineEndpoint, Model
from azure.core.exceptions import ResourceNotFoundError
from azure.identity import DefaultAzureCredential
```

#### 2. Authentication Migration

**v1 (Original):**
```python
auth = InteractiveLoginAuthentication(tenant_id='ce9f1d01-56a1-4c06-bf68-2cfbf51ff729', force=True)
ws = Workspace(
    subscription_id='60181c42-6ab1-4a45-86e2-27d2a74ff8b0',
    resource_group='ps-prod-ml-claimsauto',
    workspace_name='ps-prod-ml-ws-claimsauto',
    auth=auth,
)
```

**v2 (Migrated):**
```python
credential = DefaultAzureCredential()
client = MLClient(
    credential=credential,
    subscription_id=self.subscription_id,
    resource_group_name=self.resource_group,
    workspace_name=self.workspace_name,
)
```

#### 3. Model Registration Migration

**v1 (Original):**
```python
# model = Model.register(workspace=ws, model_name="ps-prd-aks-cav02-v1", model_path="../data/models/optimal.h5")
model = Model(workspace=ws, name='ps-prd-aks-cav02-v1')
```

**v2 (Migrated):**
```python
# Try to get existing model
try:
    model = self.client.models.get(name=self.model_name, label='latest')
except ResourceNotFoundError:
    # Create new model registration
    file_model = Model(
        name=self.model_name,
        path=str(model_path),
        type=AssetTypes.CUSTOM_MODEL,
        description=f'Model registered on {pendulum.now().isoformat()} (migrated from v1)',
    )
    model = self.client.models.create_or_update(file_model)
```

#### 4. Environment Management Migration

**v1 (Original):**
```python
# env = Environment.from_conda_specification(name='ps-prd-aks-cav02-env', file_path='../configs/tstarc_deploy_conda.yaml')
# env.register(workspace=ws)
env = Environment.get(workspace=ws, name='ps-prd-aks-cav02-env')
```

**v2 (Migrated):**
```python
# Try to get existing environment
try:
    env = self.client.environments.get(name=self.env_name, label='latest')
except ResourceNotFoundError:
    # Create new environment
    env_conda = Environment(
        image='mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04',
        conda_file=str(conda_file_path),
        name=self.env_name,
        description=f'Environment created on {pendulum.now().isoformat()} (migrated from v1)',
    )
    env = self.client.environments.create_or_update(env_conda)
```

#### 5. Deployment Configuration Migration

**v1 (Original):**
```python
inference_config = InferenceConfig(environment=env, source_directory='../python/', entry_script='score.py')

cluster_name = 'ps-prd-aks-cav02'
aks_target = AksCompute(workspace=ws, name=cluster_name)

deployment_config = AksWebservice.deploy_configuration(
    cpu_cores=1,
    memory_gb=1.5,
    enable_app_insights=True,
)
```

**v2 (Migrated):**
```python
# Create managed online endpoint (replaces AksCompute)
endpoint = ManagedOnlineEndpoint(
    name=self.endpoint_name,
    auth_mode='key',
    description=f'Endpoint created on {pendulum.now().isoformat()} (migrated from v1 AKS)',
    tags={'MigratedFrom': 'v1_AksWebservice', 'CreatedBy': 'AzureMLV1MigratedDeployer'},
)

# Create deployment (replaces InferenceConfig + AksWebservice.deploy_configuration)
deployment = ManagedOnlineDeployment(
    name=self.deployment_name,
    endpoint_name=self.endpoint_name,
    model=model,
    code_configuration=CodeConfiguration(
        code=str(code_path),
        scoring_script='score.py'  # equivalent to entry_script
    ),
    environment=environment,
    instance_type='Standard_DS3_v2',  # equivalent to cpu_cores=1, memory_gb=1.5
    instance_count=1,
    environment_variables={'PYTHONPATH': '/var/azureml-app/autolodge_retrained_deploy'},
)
```

#### 6. Deployment Execution Migration

**v1 (Original):**
```python
# Delete existing service
try:
    service = AksWebservice(name='ps-prd-aks-cav02', workspace=ws)
    service.delete()
    print('Found existing service, deleted')
except WebserviceException:
    print('No existing service found')

# Deploy model
service = Model.deploy(
    workspace=ws,
    name='ps-prd-aks-cav02',
    models=[model],
    inference_config=inference_config,
    deployment_config=deployment_config,
    deployment_target=aks_target,
    overwrite=True,
)
service.wait_for_deployment(show_output=True)
print(service.state)
```

**v2 (Migrated):**
```python
# Delete existing endpoint
try:
    endpoint = self.client.online_endpoints.get(name=self.endpoint_name)
    delete_poller = self.client.online_endpoints.begin_delete(name=self.endpoint_name)
    while not delete_poller.done():
        time.sleep(5)
except ResourceNotFoundError:
    logger.info('No existing endpoint found')

# Create endpoint
poller = self.client.online_endpoints.begin_create_or_update(endpoint)
while not poller.done():
    time.sleep(5)

# Create deployment
deployment_poller = self.client.online_deployments.begin_create_or_update(deployment)
while not deployment_poller.done():
    time.sleep(5)

# Configure traffic (new in v2)
endpoint.traffic = {self.deployment_name: 100}
traffic_poller = self.client.online_endpoints.begin_create_or_update(endpoint)
while not traffic_poller.done():
    time.sleep(3)

# Get deployment state
deployment = self.client.online_deployments.get(name=self.deployment_name, endpoint_name=self.endpoint_name)
state = getattr(deployment, 'provisioning_state', 'Unknown')
```

## Enhanced Features in v2 Migration

### 1. Environment Variable Configuration

The migrated script removes hardcoded values and uses environment variables:

```python
# Configuration with fallbacks to original v1 values
self.subscription_id = os.getenv('AZURE_SUBSCRIPTION_ID', '60181c42-6ab1-4a45-86e2-27d2a74ff8b0')
self.resource_group = os.getenv('AZURE_RESOURCE_GROUP', 'ps-prod-ml-claimsauto')
self.workspace_name = os.getenv('AZURE_ML_WORKSPACE_NAME', 'ps-prod-ml-ws-claimsauto')
self.model_name = os.getenv('MODEL_NAME', 'ps-prd-aks-cav02-v1')
self.env_name = os.getenv('ENV_NAME', 'ps-prd-aks-cav02-env')
self.endpoint_name = os.getenv('ENDPOINT_NAME', 'ps-prd-aks-cav02')
self.deployment_name = os.getenv('DEPLOYMENT_NAME', 'ps-prd-aks-cav02')
```

### 2. Comprehensive Error Handling

```python
class AzureMLDeploymentError(Exception):
    """Custom exception for Azure ML deployment errors."""
    pass

try:
    # Deployment operations
    pass
except Exception as e:
    error_msg = f'Failed to create deployment {self.deployment_name}: {str(e)}'
    logger.error(error_msg)
    raise AzureMLDeploymentError(error_msg) from e
```

### 3. Structured Logging

```python
# Configure loguru logging with timestamped log files
logger.add(
    str(log_filepath),
    format='{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}',
    level='DEBUG',
    rotation='10 MB',
    retention='30 days',
    compression='zip',
)
```

### 4. Timeout and Progress Tracking

```python
# Wait for deployment completion with timeout
start_time = time.time()
while not deployment_poller.done():
    elapsed = time.time() - start_time
    if elapsed > self.timeout_seconds:
        raise AzureMLDeploymentError(f'Deployment creation timed out after {self.timeout_seconds} seconds')

    if int(elapsed) % 30 == 0 and elapsed > 0:
        logger.info(f'Deployment creation in progress... ({elapsed:.0f}s elapsed)')

    time.sleep(5)
```

## Configuration Requirements

### Environment Variables

The migrated script requires the following environment variables (with fallbacks to original v1 values):

```bash
# Azure credentials
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group
AZURE_ML_WORKSPACE_NAME=your-workspace-name

# Deployment configuration
MODEL_NAME=your-model-name
ENV_NAME=your-environment-name
ENDPOINT_NAME=your-endpoint-name
DEPLOYMENT_NAME=your-deployment-name

# Instance configuration
INSTANCE_TYPE=Standard_DS3_v2
INSTANCE_COUNT=1
TIMEOUT_SECONDS=1800
```

### File Structure Requirements

The migrated script expects the following file structure:

```
project/
├── Python/
│   └── score.py                    # Scoring script
├── configs/
│   └── ps-dev-claimsauto-tstarc.yaml  # Conda environment file
├── resources/
│   └── models/                     # Model files directory
├── logs/                           # Log files (auto-created)
└── .env                           # Environment variables
```

## Deployment Workflow Comparison

### v1 Workflow
1. Interactive authentication with hardcoded tenant ID
2. Connect to workspace with hardcoded credentials
3. Get existing model by name
4. Get existing environment by name
5. Create inference config with source directory and entry script
6. Create AKS deployment configuration
7. Delete existing AKS web service if present
8. Deploy model to AKS cluster
9. Wait for deployment completion
10. Print service state

### v2 Workflow (Migrated)
1. Load configuration from environment variables (with v1 fallbacks)
2. Create ML client with DefaultAzureCredential
3. Register/retrieve model with proper error handling
4. Create/retrieve environment with conda file validation
5. Delete existing managed online endpoint if present
6. Create managed online endpoint
7. Create managed online deployment with code configuration
8. Configure traffic routing (100% to new deployment)
9. Get deployment state with comprehensive logging
10. Display deployment summary

## Benefits of Migration

### 1. Security Improvements
- **Removed hardcoded credentials**: Uses environment variables and DefaultAzureCredential
- **Secure authentication**: Leverages Azure's managed identity and credential chain
- **No tenant ID exposure**: Credentials are managed through Azure authentication

### 2. Modern Infrastructure
- **Managed online endpoints**: Replaces AKS clusters with managed infrastructure
- **Automatic scaling**: Built-in scaling capabilities
- **Better monitoring**: Enhanced monitoring and logging capabilities

### 3. Enhanced Reliability
- **Comprehensive error handling**: Custom exceptions and detailed error messages
- **Timeout management**: Configurable timeouts with progress tracking
- **Resource validation**: Validates file paths and configurations before deployment

### 4. Improved Maintainability
- **Environment-based configuration**: Easy to configure for different environments
- **Structured logging**: Detailed logs with rotation and compression
- **Type hints and documentation**: Better code documentation and IDE support

### 5. Operational Excellence
- **Progress tracking**: Real-time deployment progress updates
- **Deployment summaries**: Comprehensive deployment information
- **Resource cleanup**: Proper cleanup of existing resources

## Usage Examples

### Basic Usage
```bash
# Set environment variables in .env file
cp .env.example .env
# Edit .env with your configuration

# Run the migrated deployment script
python deploy/submit_deploy_v1_migrated.py
```

### Advanced Configuration
```bash
# Override specific settings
export INSTANCE_TYPE=Standard_DS4_v2
export INSTANCE_COUNT=2
export TIMEOUT_SECONDS=3600

python deploy/submit_deploy_v1_migrated.py
```

## Compatibility Notes

### Backward Compatibility
- The migrated script includes fallback values to the original v1 hardcoded values
- Maintains the same deployment workflow and functionality
- Compatible with existing scoring scripts and model files

### Breaking Changes
- Requires Azure ML SDK v2 dependencies
- Uses managed online endpoints instead of AKS web services
- Requires environment variable configuration for production use

## Migration Checklist

- [x] Replace azureml.core imports with azure.ai.ml equivalents
- [x] Replace InteractiveLoginAuthentication with DefaultAzureCredential
- [x] Replace Environment.get() with modern Environment entities
- [x] Replace AksWebservice with ManagedOnlineEndpoint
- [x] Replace InferenceConfig with CodeConfiguration
- [x] Replace Model.register() with MLClient.models.create_or_update()
- [x] Implement environment variable-based configuration
- [x] Add comprehensive error handling and logging
- [x] Add deployment validation and health checks
- [x] Implement proper resource cleanup
- [x] Add timeout management and progress tracking
- [x] Maintain same deployment workflow
- [x] Preserve scoring script integration
- [x] Add deployment monitoring capabilities

## Conclusion

The migration from Azure ML SDK v1 to v2 successfully modernizes the deployment script while maintaining full functionality. The migrated script provides enhanced security, reliability, and maintainability while preserving the original deployment workflow and compatibility with existing resources.

The migration demonstrates best practices for Azure ML SDK v2 adoption and serves as a template for migrating other v1 deployment scripts in the project.