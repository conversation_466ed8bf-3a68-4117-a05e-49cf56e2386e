# Azure ML SDK v1 to v2 Migration - Training Script

This document describes the migration of `Python/train.py` from Azure ML SDK v1 to v2 with modern MLflow tracking.

## Migration Summary

### Key Changes Made

1. **Azure ML SDK v1 → v2 Migration**:
   - Replaced `from azureml.core import Run` with `azure.ai.ml.MLClient`
   - Replaced `Run.get_context()` with MLflow tracking
   - Implemented modern authentication using `DefaultAzureCredential`

2. **MLflow Integration**:
   - Replaced `run.log()` with `mlflow.log_metric()`
   - Added comprehensive parameter logging with `mlflow.log_params()`
   - Implemented model logging with `mlflow.tensorflow.log_model()`
   - Added artifact logging for model checkpoints

3. **Enhanced Features**:
   - Environment variable-based configuration
   - Graceful fallback when MLflow is not available
   - Comprehensive error handling and logging
   - Type hints and documentation
   - Input validation for hyperparameters

### Configuration

The script now uses environment variables for Azure ML workspace configuration:

```bash
# Required for Azure ML integration
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group
AZURE_ML_WORKSPACE_NAME=your-workspace-name

# Optional
AZURE_ML_EXPERIMENT_NAME=neural-network-training
```

Copy `.env.example` to `.env` and fill in your Azure ML workspace details.

### Dependencies

The following new dependencies were added to `pyproject.toml`:
- `mlflow>=2.0.0` - For experiment tracking
- `azure-ai-ml>=1.27.1` - Azure ML SDK v2 (already present)
- `azure-identity>=1.23.0` - Modern Azure authentication (already present)

### Usage

The script maintains backward compatibility with the original command-line interface:

```bash
# Basic usage
python Python/train.py

# With custom parameters
python Python/train.py \
    --num_epochs 20 \
    --learning_rate 0.001 \
    --drop_out_rate 0.3 \
    --num_nodes 256 \
    --regularise_factor 1e-5 \
    --data_dir ./data/interim
```

### Key Features Preserved

1. **Neural Network Architecture**: Complete preservation of the original model architecture
2. **Data Pipeline**: Full compatibility with existing `utils.prepare_data` module
3. **Custom Callback**: Enhanced `LogAcc` callback with MLflow logging
4. **Model Checkpointing**: Maintained with improved artifact logging
5. **Hyperparameter Logging**: Enhanced with structured parameter tracking

### New Features Added

1. **Robust Error Handling**: Comprehensive exception handling throughout
2. **Flexible Configuration**: Environment-based workspace configuration
3. **Graceful Degradation**: Works without MLflow if not available
4. **Enhanced Logging**: Structured logging with proper log levels
5. **Model Registration**: Automatic model registration in Azure ML
6. **Artifact Management**: Systematic artifact logging and management

### Migration Benefits

1. **Future-Proof**: Uses modern Azure ML SDK v2 patterns
2. **Better Tracking**: Enhanced experiment tracking with MLflow
3. **Improved Reliability**: Better error handling and logging
4. **Enhanced Security**: Modern authentication patterns
5. **Better Maintainability**: Clean code structure with type hints

### Backward Compatibility

The script maintains full backward compatibility:
- Same command-line interface
- Same data preparation pipeline
- Same model architecture and training logic
- Same output directory structure

### Troubleshooting

1. **Import Errors**: Install dependencies with `pip install -r requirements.txt` or `uv sync`
2. **Authentication Issues**: Ensure Azure CLI is logged in or service principal is configured
3. **MLflow Not Available**: Script will run without MLflow tracking if not installed
4. **Workspace Connection**: Check environment variables in `.env` file

### Testing

To test the migration:

1. Set up environment variables in `.env`
2. Ensure data is available in the specified data directory
3. Run with minimal epochs for testing: `python Python/train.py --num_epochs 1`

The script will log its progress and indicate whether MLflow tracking is active.