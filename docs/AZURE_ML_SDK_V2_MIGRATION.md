# Azure ML SDK v1 to v2 Migration - score.py

This document outlines the migration of `Python/score.py` from Azure ML SDK v1 to v2, implementing modern MLClient patterns while preserving all inference functionality.

## Migration Summary

### Key Changes Made

#### 1. **Azure ML SDK Imports Migration**
- **Before (v1):**
  ```python
  from azureml.core import Workspace, Model
  from azureml.core.authentication import ServicePrincipalAuthentication
  ```
- **After (v2):**
  ```python
  from azure.ai.ml import MLClient
  from azure.identity import DefaultAzureCredential
  from azure.core.exceptions import ResourceNotFoundError
  ```

#### 2. **Authentication Migration**
- **Before (v1):** Service Principal Authentication with explicit credentials
  ```python
  auth = ServicePrincipalAuthentication(
      tenant_id=tenant_id,
      service_principal_id=client_id,
      service_principal_password=client_secret,
  )
  ws = Workspace(subscription_id=subscription_id, resource_group=resource_group, workspace_name=workspace_name, auth=auth)
  ```
- **After (v2):** DefaultAzureCredential with multiple authentication methods
  ```python
  credential = DefaultAzureCredential()
  client = MLClient(
      credential=credential,
      subscription_id=subscription_id,
      resource_group_name=resource_group,
      workspace_name=workspace_name,
  )
  ```

#### 3. **Model Download Migration**
- **Before (v1):**
  ```python
  model_obj = Model(workspace=ws, name=model_name, version=model_version)
  model_path = model_obj.download(target_dir='../data/models', exist_ok=True)
  ```
- **After (v2):**
  ```python
  model_asset = client.models.get(name=model_name, version=model_version)
  model_path = client.models.download(
      name=model_asset.name,
      version=str(model_asset.version),
      download_path=download_dir
  )
  ```

### New Features and Enhancements

#### 1. **Enhanced Error Handling**
- Added comprehensive exception handling for model download and loading
- Specific error types for different failure scenarios
- Detailed logging for troubleshooting

#### 2. **Model Versioning Support**
- Support for latest version retrieval using `label="latest"`
- Configurable model version via environment variables
- Backward compatibility with existing version specifications

#### 3. **Improved Configuration Management**
- New environment variables for Azure ML SDK v2:
  - `AZURE_ML_MODEL_NAME`: Model name in registry
  - `AZURE_ML_MODEL_VERSION`: Specific model version
- Backward compatibility with existing variables:
  - `MODEL_NAME`: Legacy model name
  - `MODEL_VERSION`: Legacy model version

#### 4. **Enhanced Type Safety**
- Added comprehensive type hints throughout the codebase
- Proper typing for function parameters and return values
- Better IDE support and code completion

#### 5. **Modular Architecture**
- Separated concerns into focused functions:
  - `_create_ml_client()`: MLClient creation and authentication
  - `_download_model_from_registry()`: Model download from registry
  - `_load_model_from_path()`: Model loading from filesystem
  - `_load_preprocessing_components()`: Preprocessing components loading

### Environment Variables

#### Required Variables (unchanged)
```bash
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group
AZURE_ML_WORKSPACE_NAME=your-workspace-name
```

#### New Optional Variables
```bash
# Azure ML SDK v2 Model Configuration
AZURE_ML_MODEL_NAME=tstarc-test          # Model name in registry
AZURE_ML_MODEL_VERSION=                  # Specific version (empty = latest)

# Azure Authentication (for DefaultAzureCredential)
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret
```

#### Legacy Variables (maintained for compatibility)
```bash
MODEL_NAME=tstarc-test
MODEL_VERSION=2
```

### Authentication Methods Supported

The new implementation uses `DefaultAzureCredential` which supports multiple authentication methods in order of precedence:

1. **Environment Variables** (Service Principal)
   - `AZURE_TENANT_ID`
   - `AZURE_CLIENT_ID`
   - `AZURE_CLIENT_SECRET`

2. **Managed Identity** (when running on Azure)

3. **Azure CLI** (when logged in via `az login`)

4. **Visual Studio Code** (when logged in)

5. **Azure PowerShell** (when logged in)

### Preserved Functionality

#### 1. **Complete Text Preprocessing Pipeline**
- Tokenization with custom regex patterns
- Spell checking using SymSpell
- Stemming with Porter Stemmer
- Stopword removal
- Abbreviation expansion
- All preprocessing steps remain identical

#### 2. **Model Inference Workflow**
- Multi-class prediction with top-5 results
- Confidence score calculation
- T202 rule for empty treatments
- Prediction formatting and response structure

#### 3. **API Compatibility**
- `init()` function signature unchanged
- `run()` function signature unchanged
- Response format identical (API_Version_No: 7)
- Full compatibility with existing deployment scripts

#### 4. **Utility Dependencies**
- AzureStorageUtil integration preserved
- Asset download functionality maintained
- All preprocessing file dependencies intact

### Deployment Compatibility

The migrated `score.py` remains fully compatible with existing deployment scripts:

- **submit_deploy_v1.py**: No changes required
- **submit_deploy_v2.py**: No changes required
- **submit_deploy_v2_refactored.py**: No changes required

### Error Handling Improvements

#### 1. **Model Download Errors**
```python
try:
    model_path = _download_model_from_registry(ml_client, model_name, model_version)
except ResourceNotFoundError as e:
    logger.error(f"Model '{model_name}' version '{model_version}' not found: {str(e)}")
    raise
except Exception as e:
    logger.error(f"Failed to download model: {str(e)}")
    raise
```

#### 2. **Authentication Errors**
```python
try:
    credential = DefaultAzureCredential()
    client = MLClient(...)
except Exception as e:
    logger.error(f"Failed to create MLClient: {str(e)}")
    raise
```

#### 3. **Inference Errors**
```python
try:
    pred = model.predict(x, batch_size=8)
except Exception as e:
    logger.error(f"Model prediction failed: {str(e)}")
    raise RuntimeError(f"Model prediction failed: {str(e)}")
```

### Performance Considerations

#### 1. **Model Caching**
- Models are downloaded once during initialization
- No repeated downloads during inference
- Efficient model loading with path validation

#### 2. **Connection Reuse**
- MLClient instance created once and reused
- Credential caching via DefaultAzureCredential
- Optimized authentication flow

#### 3. **Memory Management**
- Proper resource cleanup in error scenarios
- Efficient preprocessing component loading
- Minimal memory overhead for v2 SDK

### Testing and Validation

#### 1. **Local Testing Mode**
The `__main__` block provides local testing capability:
```python
if __name__ == '__main__':
    # Local testing mode - load model directly from file
    model = keras.models.load_model('../data/models/optimal.h5')
    # ... load preprocessing components
    result = run(test_data)
    print(result)
```

#### 2. **Integration Testing**
- Compatible with existing test suites
- No changes required to test data formats
- Identical response structures for validation

### Migration Benefits

#### 1. **Modern SDK Features**
- Latest Azure ML capabilities
- Improved performance and reliability
- Better error handling and diagnostics

#### 2. **Enhanced Security**
- DefaultAzureCredential best practices
- Multiple authentication method support
- Secure credential management

#### 3. **Future-Proofing**
- Azure ML SDK v2 is the current supported version
- Access to latest Azure ML features
- Long-term support and updates

#### 4. **Maintainability**
- Cleaner, more modular code structure
- Better type safety and IDE support
- Comprehensive error handling

### Troubleshooting

#### Common Issues and Solutions

1. **Authentication Failures**
   - Ensure environment variables are set correctly
   - Verify Azure CLI login status: `az login`
   - Check service principal permissions

2. **Model Not Found**
   - Verify model name and version in Azure ML Studio
   - Check model registry permissions
   - Ensure model is registered correctly

3. **Download Failures**
   - Check network connectivity
   - Verify storage permissions
   - Ensure sufficient disk space

4. **Import Errors**
   - Verify Azure ML SDK v2 installation: `pip install azure-ai-ml`
   - Check Python environment compatibility
   - Ensure all dependencies are installed

### Next Steps

1. **Test the Migration**
   - Run local tests with the migrated code
   - Validate inference results match expected outputs
   - Test with different model versions

2. **Deploy and Monitor**
   - Deploy using existing deployment scripts
   - Monitor performance and error rates
   - Validate production inference results

3. **Optimize Further**
   - Consider implementing model caching strategies
   - Explore Azure ML SDK v2 advanced features
   - Optimize for specific deployment scenarios

This migration successfully modernizes the inference service while maintaining full backward compatibility and preserving all existing functionality.