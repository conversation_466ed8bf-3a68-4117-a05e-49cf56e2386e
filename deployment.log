2025-05-29 19:43:19,142 - __main__ - INFO - Starting deployment context
2025-05-29 19:43:19,143 - __main__ - INFO - Environment configuration loaded and validated successfully
2025-05-29 19:43:19,143 - __main__ - INFO - Initialized AzureMLDeployer with configuration
2025-05-29 19:43:19,143 - __main__ - INFO - Starting deployment attempt 1/3
2025-05-29 19:43:19,143 - __main__ - INFO - Checking for existing model: ps-dev-ca-tstarc
2025-05-29 19:43:19,143 - __main__ - INFO - Creating Azure ML client connection
2025-05-29 19:43:19,144 - azure.identity._credentials.environment - INFO - Environment is configured for ClientSecretCredential
2025-05-29 19:43:19,145 - azure.identity._credentials.managed_identity - INFO - ManagedIdentityCredential will use IMDS with client_id: e34d636b-b1a1-4ec9-894d-3250d863b9e1
2025-05-29 19:43:19,345 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ce9f1d01-56a1-4c06-bf68-2cfbf51ff729/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.23.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
No body was attached to the request
2025-05-29 19:43:19,452 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': 'aed8f4ed-ab72-4176-8e44-511d8c224900'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 29 May 2025 09:43:18 GMT'
    'Content-Length': '1753'
2025-05-29 19:43:19,455 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ce9f1d01-56a1-4c06-bf68-2cfbf51ff729/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.23.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
A body is sent with the request
2025-05-29 19:43:19,526 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': 'a52955c4-24f1-4f01-8bb7-21611c1e0800'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 29 May 2025 09:43:18 GMT'
    'Content-Length': '1667'
2025-05-29 19:43:19,526 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-05-29 19:43:19,526 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5a5c43f2-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:19,713 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '3668'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': '4b92e3eb-82cb-4892-b031-ac52787efc22'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 8B880ED534F34485AC19CEBBF5A750D9 Ref B: SYD03EDGE2119 Ref C: 2025-05-29T09:43:19Z'
    'Date': 'Thu, 29 May 2025 09:43:19 GMT'
2025-05-29 19:43:19,717 - __main__ - INFO - Successfully connected to workspace: t-to-tstar
2025-05-29 19:43:19,718 - azure.identity._internal.get_token_mixin - INFO - ClientSecretCredential.get_token_info succeeded
2025-05-29 19:43:19,718 - azure.identity._internal.decorators - INFO - EnvironmentCredential.get_token_info succeeded
2025-05-29 19:43:19,718 - azure.identity._credentials.default - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-05-29 19:43:19,718 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/models/ps-dev-ca-tstarc/versions?api-version=REDACTED&$orderBy=REDACTED&$top=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5a95297e-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:20,374 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1378'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '86945d5d-a88e-41f1-b4e1-278c46bc35f9'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: F1537301419C4B1386D8F552459F7E31 Ref B: SYD03EDGE1608 Ref C: 2025-05-29T09:43:19Z'
    'Date': 'Thu, 29 May 2025 09:43:19 GMT'
2025-05-29 19:43:20,375 - __main__ - INFO - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:20,375 - __main__ - INFO - Checking for existing environment: ps-dev-ca-tstarc
2025-05-29 19:43:20,375 - azure.identity._internal.get_token_mixin - INFO - ClientSecretCredential.get_token_info succeeded
2025-05-29 19:43:20,376 - azure.identity._internal.decorators - INFO - EnvironmentCredential.get_token_info succeeded
2025-05-29 19:43:20,376 - azure.identity._credentials.default - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-05-29 19:43:20,376 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/environments/ps-dev-ca-tstarc/versions?api-version=REDACTED&$orderBy=REDACTED&$top=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5af981e4-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:21,997 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1742'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'ede744e4-8340-4446-ad6a-a01ab54b558d'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: AA6AED2F5EC0440CA8C48623B46A4A75 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:20Z'
    'Date': 'Thu, 29 May 2025 09:43:21 GMT'
2025-05-29 19:43:21,999 - __main__ - INFO - Found existing environment: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:22,000 - __main__ - INFO - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-29 19:43:22,000 - azure.identity._internal.get_token_mixin - INFO - ClientSecretCredential.get_token_info succeeded
2025-05-29 19:43:22,000 - azure.identity._internal.decorators - INFO - EnvironmentCredential.get_token_info succeeded
2025-05-29 19:43:22,000 - azure.identity._credentials.default - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-05-29 19:43:22,001 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5bf17124-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:22,138 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1793'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': 'b779e6c5-93e4-4b86-a5e5-f59193086e5b'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: C645F9BEA279487E880361716CFFB60B Ref B: SYD03EDGE1322 Ref C: 2025-05-29T09:43:22Z'
    'Date': 'Thu, 29 May 2025 09:43:22 GMT'
2025-05-29 19:43:22,140 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc/deployments?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5c06b782-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:22,707 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '2499'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-original-request-ids': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '********-f5a1-418a-964d-28a5152271bf'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 80AAE5047E2349CF8B179FB246B9FA76 Ref B: SYD03EDGE1322 Ref C: 2025-05-29T09:43:22Z'
    'Date': 'Thu, 29 May 2025 09:43:22 GMT'
2025-05-29 19:43:22,709 - __main__ - INFO - Found existing endpoint: ps-dev-ca-tstarc
2025-05-29 19:43:22,709 - __main__ - INFO - Creating deployment: ps-dev-ca-tstarc
2025-05-29 19:43:22,709 - __main__ - INFO - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:22,709 - __main__ - INFO - Using environment: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:22,709 - __main__ - INFO - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-29 19:43:22,710 - __main__ - INFO - Starting deployment creation...
2025-05-29 19:43:22,713 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5c5e411e-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:22,835 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1793'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': '8f5d8e3c-5e72-47a1-82bf-f91e32edd517'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: D8B881834A76490199DB2C90BC8D0E80 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:22Z'
    'Date': 'Thu, 29 May 2025 09:43:21 GMT'
2025-05-29 19:43:22,838 - azure.identity._internal.get_token_mixin - INFO - ClientSecretCredential.get_token_info succeeded
2025-05-29 19:43:22,838 - azure.identity._internal.decorators - INFO - EnvironmentCredential.get_token_info succeeded
2025-05-29 19:43:22,838 - azure.identity._credentials.default - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-05-29 19:43:22,838 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/codes/2e54a4a3-4375-44fb-8b51-be3b02a34ec1/versions?api-version=REDACTED&hash=REDACTED&hashVersion=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5c714264-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:24,078 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1195'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '58cdec93-3cbf-4e43-98cb-7db7486406c3'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A183F9A850824663AE23E182E63D7D58 Ref B: SYD03EDGE2120 Ref C: 2025-05-29T09:43:22Z'
    'Date': 'Thu, 29 May 2025 09:43:23 GMT'
2025-05-29 19:43:24,080 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/codes/180a2ef4-70d8-41bc-873c-c40853ed816a/versions/1?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5d2eb5e2-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:24,374 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1074'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '41781e75-5b6e-4ad1-919d-acbb81bbaefe'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 2B089723380445C0ABAA79FA5DB8294A Ref B: SYD03EDGE2120 Ref C: 2025-05-29T09:43:24Z'
    'Date': 'Thu, 29 May 2025 09:43:24 GMT'
2025-05-29 19:43:24,376 - azure.identity._internal.get_token_mixin - INFO - ClientSecretCredential.get_token_info succeeded
2025-05-29 19:43:24,376 - azure.identity._internal.decorators - INFO - EnvironmentCredential.get_token_info succeeded
2025-05-29 19:43:24,376 - azure.identity._credentials.default - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-05-29 19:43:24,376 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/datastores/workspaceblobstore?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5d5bee54-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:24,594 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '919'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'f24d2774-20f4-49c1-baaf-d0e9ad686bdb'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: D20504BEF9BD45289B039E4D4A96B3B1 Ref B: SYD03EDGE1019 Ref C: 2025-05-29T09:43:24Z'
    'Date': 'Thu, 29 May 2025 09:43:24 GMT'
2025-05-29 19:43:24,595 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5d7d70e2-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:24,708 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '3668'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': 'f5ce5ab0-552f-4ff6-80df-839d46941468'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: ******************************** Ref B: SYD03EDGE2119 Ref C: 2025-05-29T09:43:24Z'
    'Date': 'Thu, 29 May 2025 09:43:24 GMT'
2025-05-29 19:43:24,710 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc/deployments/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'PUT'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '1175'
    'Accept': 'application/json'
    'x-ms-client-request-id': '5d8ee03e-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-05-29 19:43:27,054 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 400
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1406'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '24'
    'x-ms-request-id': '9c11fb26-4ad9-4b79-9111-c4b785a9aa85'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: EBBCAC23BEA34BDAB26E40148CF8ED1B Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:24Z'
    'Date': 'Thu, 29 May 2025 09:43:26 GMT'
2025-05-29 19:43:27,055 - __main__ - ERROR - Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-d9bebbc5c0009bf82359a6bab4b54ba6-10aa5c3d045e41ad-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-d9bebbc5c0009bf82359a6bab4b54ba6-10aa5c3d045e41ad-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "d9bebbc5c0009bf82359a6bab4b54ba6",
        "request": "7f488580adc4f849"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:27.041788+00:00"
}
2025-05-29 19:43:27,055 - __main__ - ERROR - Deployment attempt 1 failed: Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-d9bebbc5c0009bf82359a6bab4b54ba6-10aa5c3d045e41ad-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-d9bebbc5c0009bf82359a6bab4b54ba6-10aa5c3d045e41ad-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "d9bebbc5c0009bf82359a6bab4b54ba6",
        "request": "7f488580adc4f849"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:27.041788+00:00"
}
2025-05-29 19:43:27,055 - __main__ - INFO - Waiting 1.0 seconds before retry (attempt 1)
2025-05-29 19:43:28,055 - __main__ - INFO - Retrying deployment (attempt 2/3)
2025-05-29 19:43:28,056 - __main__ - INFO - Starting deployment attempt 2/3
2025-05-29 19:43:28,056 - __main__ - INFO - Checking for existing model: ps-dev-ca-tstarc
2025-05-29 19:43:28,056 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/models/ps-dev-ca-tstarc/versions?api-version=REDACTED&$orderBy=REDACTED&$top=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5f8d814c-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:28,251 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1378'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'c271e3ea-a08c-4be7-ad73-cae3d3db18dc'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A0089C5B30B348D3A1C48032C175D327 Ref B: SYD03EDGE1608 Ref C: 2025-05-29T09:43:28Z'
    'Date': 'Thu, 29 May 2025 09:43:27 GMT'
2025-05-29 19:43:28,251 - __main__ - INFO - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:28,252 - __main__ - INFO - Checking for existing environment: ps-dev-ca-tstarc
2025-05-29 19:43:28,252 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/environments/ps-dev-ca-tstarc/versions?api-version=REDACTED&$orderBy=REDACTED&$top=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5fab631a-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:28,397 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1742'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '70d89443-17f5-45a2-81be-9c90996be590'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 79BB233D4DD94B0880137D48780B2FE8 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:28Z'
    'Date': 'Thu, 29 May 2025 09:43:27 GMT'
2025-05-29 19:43:28,398 - __main__ - INFO - Found existing environment: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:28,399 - __main__ - INFO - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-29 19:43:28,399 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5fc1d50a-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:28,561 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1793'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': '2db5fad6-991e-4942-b87c-8440fc9fb01b'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: D337AEE44F5F4CBC9DADC05666972EB2 Ref B: SYD03EDGE1322 Ref C: 2025-05-29T09:43:28Z'
    'Date': 'Thu, 29 May 2025 09:43:28 GMT'
2025-05-29 19:43:28,563 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc/deployments?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5fdac6be-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:28,695 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '2499'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-original-request-ids': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'ea09b3d4-a4a5-44fe-aa9d-fe95b23a440c'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 92845DDA007146068EA833A2C5760FC0 Ref B: SYD03EDGE1322 Ref C: 2025-05-29T09:43:28Z'
    'Date': 'Thu, 29 May 2025 09:43:28 GMT'
2025-05-29 19:43:28,696 - __main__ - INFO - Found existing endpoint: ps-dev-ca-tstarc
2025-05-29 19:43:28,696 - __main__ - INFO - Creating deployment: ps-dev-ca-tstarc
2025-05-29 19:43:28,696 - __main__ - INFO - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:28,696 - __main__ - INFO - Using environment: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:28,696 - __main__ - INFO - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-29 19:43:28,697 - __main__ - INFO - Starting deployment creation...
2025-05-29 19:43:28,702 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5ff001d2-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:28,798 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1793'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': '151b3779-768a-4b76-a685-fae0a2fdf3b1'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 3911136E7B1944FF800A32039234BC96 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:28Z'
    'Date': 'Thu, 29 May 2025 09:43:27 GMT'
2025-05-29 19:43:28,803 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/codes/5c6867d4-cd07-41c6-abcb-4ba34a8f04ab/versions?api-version=REDACTED&hash=REDACTED&hashVersion=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '5fff80c6-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:29,039 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1195'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '248'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '80c6189a-9124-48d2-8b2f-f19ead562e13'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: F48F46ADC62F4B7AB4D6826E6B2FC0D8 Ref B: SYD03EDGE2120 Ref C: 2025-05-29T09:43:28Z'
    'Date': 'Thu, 29 May 2025 09:43:28 GMT'
2025-05-29 19:43:29,040 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/codes/180a2ef4-70d8-41bc-873c-c40853ed816a/versions/1?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '60239f10-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:29,705 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1074'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '99e14056-b4fe-4880-99f5-2dfb7fd108d4'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: E0E1C804A32845C6A0FE1F5D10D5FD45 Ref B: SYD03EDGE2120 Ref C: 2025-05-29T09:43:29Z'
    'Date': 'Thu, 29 May 2025 09:43:29 GMT'
2025-05-29 19:43:29,706 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/datastores/workspaceblobstore?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '6089371c-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:29,857 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '919'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'ce584f85-b609-462e-96d9-f3d873c87596'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A2EEC5B26ED54B14BE8FA62DDBCA9ED2 Ref B: SYD03EDGE1019 Ref C: 2025-05-29T09:43:29Z'
    'Date': 'Thu, 29 May 2025 09:43:29 GMT'
2025-05-29 19:43:29,859 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '60a086d8-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:29,937 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '3668'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': 'f215ae13-0add-4630-857b-5f3bb9d6fa02'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 2EF1394AB00B429E9F8351FE22408B8F Ref B: SYD03EDGE2119 Ref C: 2025-05-29T09:43:29Z'
    'Date': 'Thu, 29 May 2025 09:43:29 GMT'
2025-05-29 19:43:29,939 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc/deployments/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'PUT'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '1175'
    'Accept': 'application/json'
    'x-ms-client-request-id': '60acd834-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-05-29 19:43:32,383 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 400
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1407'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '24'
    'x-ms-request-id': '15cdb495-3f80-411e-944e-f96daf8cbfde'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 9C9912C2165849079906D60FA928FA76 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:29Z'
    'Date': 'Thu, 29 May 2025 09:43:31 GMT'
2025-05-29 19:43:32,384 - __main__ - ERROR - Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-cd2c759b540c98830d25326149a5aac2-70c0f115b954f751-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-cd2c759b540c98830d25326149a5aac2-70c0f115b954f751-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "cd2c759b540c98830d25326149a5aac2",
        "request": "e5bf7ee89bab341e"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:32.3374303+00:00"
}
2025-05-29 19:43:32,384 - __main__ - ERROR - Deployment attempt 2 failed: Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-cd2c759b540c98830d25326149a5aac2-70c0f115b954f751-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-cd2c759b540c98830d25326149a5aac2-70c0f115b954f751-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "cd2c759b540c98830d25326149a5aac2",
        "request": "e5bf7ee89bab341e"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:32.3374303+00:00"
}
2025-05-29 19:43:32,384 - __main__ - INFO - Waiting 2.0 seconds before retry (attempt 2)
2025-05-29 19:43:34,384 - __main__ - INFO - Retrying deployment (attempt 3/3)
2025-05-29 19:43:34,384 - __main__ - INFO - Starting deployment attempt 3/3
2025-05-29 19:43:34,384 - __main__ - INFO - Checking for existing model: ps-dev-ca-tstarc
2025-05-29 19:43:34,385 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/models/ps-dev-ca-tstarc/versions?api-version=REDACTED&$orderBy=REDACTED&$top=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '635332ea-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:34,756 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1378'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '********-e3d2-4fe9-83ef-b04df9aa9c48'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 997452624CDA4438994FCADF58377F7E Ref B: SYD03EDGE1608 Ref C: 2025-05-29T09:43:34Z'
    'Date': 'Thu, 29 May 2025 09:43:33 GMT'
2025-05-29 19:43:34,757 - __main__ - INFO - Found existing model: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:34,757 - __main__ - INFO - Checking for existing environment: ps-dev-ca-tstarc
2025-05-29 19:43:34,758 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/environments/ps-dev-ca-tstarc/versions?api-version=REDACTED&$orderBy=REDACTED&$top=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '638c1362-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:35,202 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1742'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': '3364d5de-61b4-4a51-9bd7-92c979ae3f9e'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A48A08FB13CA478DB312E1F0DBD63997 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:34Z'
    'Date': 'Thu, 29 May 2025 09:43:34 GMT'
2025-05-29 19:43:35,204 - __main__ - INFO - Found existing environment: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:35,205 - __main__ - INFO - Checking for existing endpoint: ps-dev-ca-tstarc
2025-05-29 19:43:35,205 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '63d05da6-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:35,328 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1793'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': '7f7a4349-7c00-49ba-bc3d-2688adc2c88c'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: BC8A37C2B90940568BC5A1798EE77954 Ref B: SYD03EDGE1322 Ref C: 2025-05-29T09:43:35Z'
    'Date': 'Thu, 29 May 2025 09:43:35 GMT'
2025-05-29 19:43:35,329 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc/deployments?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '63e344ac-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:35,408 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '2499'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-original-request-ids': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'c2f79e50-f98a-4d43-8a0e-685238e69e84'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 7DA491FED0D048D2A4ED08CE2A905CBE Ref B: SYD03EDGE1322 Ref C: 2025-05-29T09:43:35Z'
    'Date': 'Thu, 29 May 2025 09:43:35 GMT'
2025-05-29 19:43:35,409 - __main__ - INFO - Found existing endpoint: ps-dev-ca-tstarc
2025-05-29 19:43:35,409 - __main__ - INFO - Creating deployment: ps-dev-ca-tstarc
2025-05-29 19:43:35,409 - __main__ - INFO - Using model: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:35,409 - __main__ - INFO - Using environment: ps-dev-ca-tstarc (version: 1)
2025-05-29 19:43:35,409 - __main__ - INFO - Using code path: /home/<USER>/repos/autolodge_retrained_deploy/Python
2025-05-29 19:43:35,410 - __main__ - INFO - Starting deployment creation...
2025-05-29 19:43:35,413 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '63f012f4-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:35,492 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1793'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': '25557b87-ceec-4696-a0bb-b300cec4c895'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 552B29************************** Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:35Z'
    'Date': 'Thu, 29 May 2025 09:43:34 GMT'
2025-05-29 19:43:35,496 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/codes/e9640262-29a3-4c65-8111-a698131601af/versions?api-version=REDACTED&hash=REDACTED&hashVersion=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '63fcb1e4-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:35,849 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1195'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'a16dc3dc-b50f-4ed1-9bf1-7721ae9eb7f6'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 4DDF1D7F26274F71A97A7F493BD8029A Ref B: SYD03EDGE2120 Ref C: 2025-05-29T09:43:35Z'
    'Date': 'Thu, 29 May 2025 09:43:35 GMT'
2025-05-29 19:43:35,850 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/codes/180a2ef4-70d8-41bc-873c-c40853ed816a/versions/1?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '6432b9f6-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:35,975 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1074'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'ae11b202-7284-4b29-a83d-9e191e37163a'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 22E2E59686DD4579B0D09EF31753B63B Ref B: SYD03EDGE2120 Ref C: 2025-05-29T09:43:35Z'
    'Date': 'Thu, 29 May 2025 09:43:35 GMT'
2025-05-29 19:43:35,977 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/datastores/workspaceblobstore?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '64460d80-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:36,142 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '919'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '249'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-request-id': 'a50b298b-6682-478c-ae74-b02bbb5c7612'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 8CAA9BF4537F4E1D93F22E616BD122A4 Ref B: SYD03EDGE1019 Ref C: 2025-05-29T09:43:35Z'
    'Date': 'Thu, 29 May 2025 09:43:35 GMT'
2025-05-29 19:43:36,143 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar?api-version=REDACTED'
Request method: 'GET'
Request headers:
    'Accept': 'application/json'
    'x-ms-client-request-id': '645f8436-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-05-29 19:43:36,250 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '3668'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-global-reads': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-reads': '248'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-request-id': 'c82ef5c3-c95c-47f5-a646-4d8c25d00119'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: D1E36590B9674486A0E37D6B16E4EB11 Ref B: SYD03EDGE2119 Ref C: 2025-05-29T09:43:36Z'
    'Date': 'Thu, 29 May 2025 09:43:35 GMT'
2025-05-29 19:43:36,252 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/b15ae5d0-8f07-4cfb-aca3-508d38e9d983/resourceGroups/t-to-tstar-rg/providers/Microsoft.MachineLearningServices/workspaces/t-to-tstar/onlineEndpoints/ps-dev-ca-tstarc/deployments/ps-dev-ca-tstarc?api-version=REDACTED'
Request method: 'PUT'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '1175'
    'Accept': 'application/json'
    'x-ms-client-request-id': '********-3c71-11f0-b5bf-677fddee2890'
    'User-Agent': 'azure-ai-ml/1.27.1 azsdk-python-mgmt-machinelearningservices/0.1.0 Python/3.11.11 (Linux-6.8.0-1029-azure-x86_64-with-glibc2.35)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-05-29 19:43:36,914 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 400
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '1407'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'x-ms-operation-identifier': 'REDACTED'
    'Request-Context': 'REDACTED'
    'x-ms-response-type': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'azureml-served-by-cluster': 'REDACTED'
    'x-request-time': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '199'
    'x-ms-request-id': '7f81a841-59df-4f11-9e4e-481e2757a948'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 80F87A6C64C5426FAE3F8F1D6474B629 Ref B: SYD03EDGE1113 Ref C: 2025-05-29T09:43:36Z'
    'Date': 'Thu, 29 May 2025 09:43:36 GMT'
2025-05-29 19:43:36,915 - __main__ - ERROR - Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "7bd3e88c67d75dd8f4a9a53f5900f387",
        "request": "0b97bca39e80ee6e"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:36.8859739+00:00"
}
2025-05-29 19:43:36,915 - __main__ - ERROR - Deployment attempt 3 failed: Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "7bd3e88c67d75dd8f4a9a53f5900f387",
        "request": "0b97bca39e80ee6e"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:36.8859739+00:00"
}
2025-05-29 19:43:36,915 - __main__ - ERROR - All deployment attempts failed
2025-05-29 19:43:36,916 - __main__ - ERROR - Deployment context failed: Deployment failed after 3 attempts. Last error: Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "7bd3e88c67d75dd8f4a9a53f5900f387",
        "request": "0b97bca39e80ee6e"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:36.8859739+00:00"
}
2025-05-29 19:43:36,916 - __main__ - INFO - Deployment context cleanup completed
2025-05-29 19:43:36,916 - __main__ - ERROR - Deployment failed: Deployment failed after 3 attempts. Last error: Failed to create deployment ps-dev-ca-tstarc: (BadRequest) The request is invalid.
Code: BadRequest
Message: The request is invalid.
Exception Details:	(InferencingClientCallFailed) {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
	Code: InferencingClientCallFailed
	Message: {"error":{"code":"Validation","message":"{\"errors\":{\"\":[\"Specified deployment [ps-dev-ca-tstarc] failed during initial provisioning and is in an unrecoverable state. Delete and re-create.\"]},\"type\":\"https://tools.ietf.org/html/rfc9110#section-15.5.1\",\"title\":\"One or more validation errors occurred.\",\"status\":400,\"traceId\":\"00-7bd3e88c67d75dd8f4a9a53f5900f387-5a9625fb075126df-01\"}"}}
Additional Information:Type: ComponentName
Info: {
    "value": "managementfrontend"
}Type: Correlation
Info: {
    "value": {
        "operation": "7bd3e88c67d75dd8f4a9a53f5900f387",
        "request": "0b97bca39e80ee6e"
    }
}Type: Environment
Info: {
    "value": "australiaeast"
}Type: Location
Info: {
    "value": "australiaeast"
}Type: Time
Info: {
    "value": "2025-05-29T09:43:36.8859739+00:00"
}
