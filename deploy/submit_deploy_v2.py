import os
import time

import pendulum
from azure.ai.ml import M<PERSON><PERSON>
from azure.ai.ml.constants import AssetTypes
from azure.ai.ml.entities import CodeConfiguration, Environment, ManagedOnlineDeployment, ManagedOnlineEndpoint, Model
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv
from loguru import logger

load_dotenv()

# Configure loguru logger with timestamped filename
timestamp = pendulum.now().format("YYYY-MM-DD_HH-mm-ss")
log_filename = f"logs/deployment_{timestamp}.log"

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Configure loguru logger
logger.add(log_filename, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
logger.info("Starting Azure ML deployment script")
logger.info(f"Logging to: {log_filename}")


def get_ml_client() -> MLClient:
    """Create and return an Azure ML client."""
    logger.info('Initializing Azure ML client')

    subscription_id = os.getenv('AZURE_SUBSCRIPTION_ID')
    resource_group = os.getenv('AZURE_RESOURCE_GROUP')
    workspace = os.getenv('AZURE_ML_WORKSPACE_NAME')

    # Validate required environment variables
    if not subscription_id:
        raise ValueError('AZURE_SUBSCRIPTION_ID environment variable is required')
    if not resource_group:
        raise ValueError('AZURE_RESOURCE_GROUP environment variable is required')
    if not workspace:
        raise ValueError('AZURE_ML_WORKSPACE_NAME environment variable is required')

    logger.debug(f'Using subscription: {subscription_id}')
    logger.debug(f'Using resource group: {resource_group}')
    logger.debug(f'Using workspace: {workspace}')

    try:
        client = MLClient(
            DefaultAzureCredential(),
            subscription_id,
            resource_group,
            workspace,
        )
        logger.success('Azure ML client initialized successfully')
        return client
    except Exception as e:
        logger.error(f'Failed to initialize Azure ML client: {e}')
        raise


def register_model(client: MLClient) -> Model:
    """Register or retrieve an existing model."""
    local_model_name = os.getenv('MODEL_NAME')
    if not local_model_name:
        raise ValueError('MODEL_NAME environment variable is required')

    logger.info(f'Registering/retrieving model: {local_model_name}')

    try:
        # Check if model already exists
        for m in client.models.list():
            if m.name == local_model_name:
                logger.info(f"Model '{local_model_name}' already exists, using existing model")
                logger.debug(f'Existing model version: {m.version}')
                return m

        # Create new model if it doesn't exist
        logger.info(f"Model '{local_model_name}' not found, creating new model")
        file_model = Model(name=local_model_name, path='resources/models', type=AssetTypes.CUSTOM_MODEL)
        model = client.models.create_or_update(file_model)
        logger.success(f"Model '{local_model_name}' registered successfully with version: {model.version}")
        return model
    except Exception as e:
        logger.error(f"Failed to register model '{local_model_name}': {e}")
        raise


def create_env(client: MLClient) -> Environment:
    """Create or retrieve an existing environment."""
    env_name = os.getenv('ENV_NAME')
    if not env_name:
        raise ValueError('ENV_NAME environment variable is required')

    logger.info(f'Creating/retrieving environment: {env_name}')

    try:
        # Check if environment already exists
        for e in list(client.environments.list()):
            if e.name == env_name:
                logger.info(f"Environment '{env_name}' already exists, using existing environment")
                logger.debug(f'Existing environment version: {e.version}')
                return e

        # Create new environment if it doesn't exist
        logger.info(f"Environment '{env_name}' not found, creating new environment")
        env_conda = Environment(
            image='mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04',
            conda_file='configs/ps-dev-claimsauto-tstarc.yaml',
            name=env_name,
        )
        env = client.environments.create_or_update(env_conda)
        logger.success(f"Environment '{env_name}' created successfully with version: {env.version}")
        return env
    except Exception as e:
        logger.error(f"Failed to create environment '{env_name}': {e}")
        raise


def deploy_endpoint(client: MLClient) -> ManagedOnlineEndpoint:
    """Deploy or update an online endpoint."""
    endpoint_name = os.getenv('ENDPOINT_NAME')
    if not endpoint_name:
        raise ValueError('ENDPOINT_NAME environment variable is required')

    logger.info(f'Deploying endpoint: {endpoint_name}')

    try:
        endpoint = ManagedOnlineEndpoint(name=endpoint_name, auth_mode='key')
        logger.debug('Created endpoint configuration with auth_mode: key')

        logger.info('Starting endpoint creation/update...')
        poller = client.online_endpoints.begin_create_or_update(endpoint)

        while not poller.done():
            logger.debug('Waiting for endpoint deployment to complete...')
            time.sleep(3)

        result_endpoint = client.online_endpoints.get(name=endpoint_name)
        logger.success(f"Endpoint '{endpoint_name}' deployed successfully")
        logger.debug(f'Endpoint provisioning state: {result_endpoint.provisioning_state}')
        # Cast to ManagedOnlineEndpoint since we know it's the correct type
        return result_endpoint  # type: ignore
    except Exception as e:
        logger.error(f"Failed to deploy endpoint '{endpoint_name}': {e}")
        raise


def deploy(client: MLClient) -> None:
    """Deploy the model to Azure ML endpoint."""
    logger.info('Starting model deployment process')

    env_name = os.getenv('ENV_NAME')
    model_name = os.getenv('MODEL_NAME')
    deployment_name = os.getenv('DEPLOYMENT_NAME')
    endpoint_name = os.getenv('ENDPOINT_NAME')

    # Validate required environment variables
    if not env_name:
        raise ValueError('ENV_NAME environment variable is required')
    if not model_name:
        raise ValueError('MODEL_NAME environment variable is required')
    if not deployment_name:
        raise ValueError('DEPLOYMENT_NAME environment variable is required')
    if not endpoint_name:
        raise ValueError('ENDPOINT_NAME environment variable is required')

    logger.info('Deployment configuration:')
    logger.info(f'  Environment: {env_name}')
    logger.info(f'  Model: {model_name}')
    logger.info(f'  Deployment: {deployment_name}')
    logger.info(f'  Endpoint: {endpoint_name}')

    try:
        # Get environment and model
        logger.info('Retrieving environment and model...')
        env = client.environments.get(name=env_name, label='latest')
        model = client.models.get(name=model_name, label='latest')
        logger.debug(f'Using environment version: {env.version}')
        logger.debug(f'Using model version: {model.version}')

        # Deploy endpoint
        _online_endpoint = deploy_endpoint(client)

        # Create deployment configuration
        logger.info('Creating deployment configuration...')
        deployment = ManagedOnlineDeployment(
            name=deployment_name,
            endpoint_name=endpoint_name,
            model=model,
            code_configuration=CodeConfiguration(code='../Python', scoring_script='score.py'),
            environment=env,
            instance_type='Standard_DS3_v2',
            instance_count=1,
            # egress_public_network_access="disabled",  # set this to "disabled" when private access is required
            environment_variables={'PYTHONPATH': '/var/azureml-app/autolodge_retrained_deploy'},
            tags={'DeploymentDate': pendulum.now().isoformat()},
        )

        logger.info('Starting deployment creation...')
        deployment_poller = client.online_deployments.begin_create_or_update(deployment)

        while not deployment_poller.done():
            logger.debug('Waiting for deployment to complete...')
            time.sleep(5)

        logger.success(f"Deployment '{deployment_name}' completed successfully")

        # Configure traffic routing
        logger.info('Configuring traffic routing...')
        online_endpoint = client.online_endpoints.get(endpoint_name)
        online_endpoint.traffic = {deployment_name: 100}
        traffic_poller = client.online_endpoints.begin_create_or_update(online_endpoint)

        while not traffic_poller.done():
            logger.debug('Waiting for traffic configuration to complete...')
            time.sleep(3)

        traffic_percent = online_endpoint.traffic[deployment_name]
        logger.success(f'Traffic routing configured: {deployment_name} = {traffic_percent}%')
        logger.info('Model deployment process completed successfully')

    except Exception as e:
        logger.error(f'Deployment failed: {e}')
        raise


if __name__ == '__main__':
    try:
        logger.info('=== Azure ML Deployment Started ===')

        # Initialize client
        ml_client = get_ml_client()

        # Register model
        model = register_model(ml_client)

        # Create environment
        environment = create_env(ml_client)

        # Deploy model
        deploy(ml_client)

        logger.success('=== Azure ML Deployment Completed Successfully ===')

    except Exception as e:
        logger.critical(f'Deployment script failed with error: {e}')
        raise
