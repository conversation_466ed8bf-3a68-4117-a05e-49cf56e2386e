from azureml.core import Environment, Workspace
from azureml.core.authentication import InteractiveLoginAuthentication
from azureml.core.compute import AksCompute
from azureml.core.model import InferenceConfig, Model
from azureml.core.webservice import AksWebservice
from azureml.exceptions import WebserviceException

# 0. Connect to workspace
auth = InteractiveLoginAuthentication(tenant_id='ce9f1d01-56a1-4c06-bf68-2cfbf51ff729', force=True)
ws = Workspace(
    subscription_id='60181c42-6ab1-4a45-86e2-27d2a74ff8b0',
    resource_group='ps-prod-ml-claimsauto',
    workspace_name='ps-prod-ml-ws-claimsauto',
    auth=auth,
)

# 1. Register the model if not done yet
# model = Model.register(workspace=ws, model_name="ps-prd-aks-cav02-v1", model_path="../data/models/optimal.h5")


# 2. Prepare a new env, create one if not done yet, otherwise load the existing one
# env = Environment.from_conda_specification(name='ps-prd-aks-cav02-env', file_path='../configs/tstarc_deploy_conda.yaml')
# env.register(workspace=ws)

# Load existing env
env = Environment.get(workspace=ws, name='ps-prd-aks-cav02-env')

# 3. Define an inference configuration
inference_config = InferenceConfig(environment=env, source_directory='../python/', entry_script='score.py')

# 4. Define a deployment configuration
cluster_name = 'ps-prd-aks-cav02'
aks_target = AksCompute(workspace=ws, name=cluster_name)

deployment_config = AksWebservice.deploy_configuration(
    cpu_cores=1,
    memory_gb=1.5,
    enable_app_insights=True,
)

# 4. Deploy the model to the cloud
try:
    service = AksWebservice(name='ps-prd-aks-cav02', workspace=ws)
    service.delete()
    print('Found existing service, deleted')
except WebserviceException:
    print('No existing service found')

model = Model(workspace=ws, name='ps-prd-aks-cav02-v1')
service = Model.deploy(
    workspace=ws,
    name='ps-prd-aks-cav02',
    models=[model],
    inference_config=inference_config,
    deployment_config=deployment_config,
    deployment_target=aks_target,
    overwrite=True,
)
service.wait_for_deployment(show_output=True)
print(service.state)
