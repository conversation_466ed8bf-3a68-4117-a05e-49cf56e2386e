#!/usr/bin/env python3
"""
Azure ML Deployment Script - Migrated from SDK v1 to v2

This script migrates the original submit_deploy_v1.py from Azure ML SDK v1 to v2,
implementing modern deployment patterns while maintaining the same deployment functionality.

Migration Changes:
1. AZURE ML SDK v1 TO v2 MIGRATION:
   - Replaced azureml.core imports with azure.ai.ml equivalents
   - Replaced InteractiveLoginAuthentication with DefaultAzureCredential
   - Replaced Environment.from_conda_specification() and Environment.get() with modern Environment entities
   - Replaced AksWebservice and InferenceConfig with ManagedOnlineEndpoint and ManagedOnlineDeployment
   - Replaced Model.register() with MLClient.models.create_or_update()

2. MODERN AZURE ML SDK v2 PATTERNS:
   - Uses MLClient for all Azure ML operations
   - Implements ManagedOnlineEndpoint for endpoint management
   - Uses ManagedOnlineDeployment for deployment configuration
   - Replaces InferenceConfig with CodeConfiguration
   - Uses modern environment management with Conda specifications

3. PRESERVED FUNCTIONALITY:
   - Maintains the same deployment workflow (delete existing service, create new one)
   - Keeps the same model registration and environment setup logic
   - Preserves the deployment target (migrated from AKS to managed online endpoints)
   - Maintains the same scoring script integration (score.py)

4. ENHANCED WITH v2 BEST PRACTICES:
   - Environment variable-based configuration (removes hardcoded values)
   - Comprehensive error handling and logging
   - Structured logging with proper levels
   - Deployment validation and health checks
   - Proper resource cleanup

Author: Migrated from Azure ML SDK v1 to v2
Date: 2025-05-29
"""

import os
import sys
import time
from pathlib import Path
from typing import Optional

# Third-party imports
import pendulum
from azure.ai.ml import MLClient
from azure.ai.ml.constants import AssetTypes
from azure.ai.ml.entities import CodeConfiguration, Environment, ManagedOnlineDeployment, ManagedOnlineEndpoint, Model
from azure.core.exceptions import ResourceNotFoundError
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv
from loguru import logger


def setup_logging() -> str:
    """
    Configure loguru logging with timestamped log files.

    Returns:
        Path to the log file created for this deployment run
    """
    # Remove default handler
    logger.remove()

    # Create logs directory if it doesn't exist
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)

    # Generate timestamp for log filename
    timestamp = pendulum.now().format('YYYY-MM-DD_HH-mm-ss')
    log_filename = f'deployment_v1_migrated_{timestamp}.log'
    log_filepath = logs_dir / log_filename

    # Add console handler with colored output
    logger.add(
        sys.stdout,
        format='<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>',
        level='INFO',
        colorize=True,
    )

    # Add file handler with detailed format
    logger.add(
        str(log_filepath),
        format='{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}',
        level='DEBUG',
        rotation='10 MB',
        retention='30 days',
        compression='zip',
    )

    logger.info(f'Logging configured - Log file: {log_filepath}')
    return str(log_filepath)


# Initialize logging
log_file_path = setup_logging()


class AzureMLDeploymentError(Exception):
    """Custom exception for Azure ML deployment errors."""
    pass


class AzureMLV1MigratedDeployer:
    """
    Azure ML deployment manager migrated from SDK v1 to v2.

    This class maintains the same deployment workflow as the original v1 script
    but uses modern Azure ML SDK v2 patterns and best practices.
    """

    def __init__(self) -> None:
        """Initialize the deployer with environment-based configuration."""
        self._client: Optional[MLClient] = None
        self._load_configuration()
        logger.info('Initialized AzureMLV1MigratedDeployer')

    def _load_configuration(self) -> None:
        """Load configuration from environment variables."""
        # Load environment variables from .env file
        load_dotenv()

        # Azure credentials (with fallback to original v1 values for compatibility)
        self.subscription_id = os.getenv('AZURE_SUBSCRIPTION_ID', '60181c42-6ab1-4a45-86e2-27d2a74ff8b0')
        self.resource_group = os.getenv('AZURE_RESOURCE_GROUP', 'ps-prod-ml-claimsauto')
        self.workspace_name = os.getenv('AZURE_ML_WORKSPACE_NAME', 'ps-prod-ml-ws-claimsauto')

        # Model and environment configuration (with fallback to original v1 values)
        self.model_name = os.getenv('MODEL_NAME', 'ps-prd-aks-cav02-v1')
        self.env_name = os.getenv('ENV_NAME', 'ps-prd-aks-cav02-env')
        self.endpoint_name = os.getenv('ENDPOINT_NAME', 'ps-prd-aks-cav02')
        self.deployment_name = os.getenv('DEPLOYMENT_NAME', 'ps-prd-aks-cav02')

        # Deployment configuration (with fallback to original v1 values)
        self.instance_type = os.getenv('INSTANCE_TYPE', 'Standard_DS3_v2')  # Equivalent to 1 CPU, 1.5GB memory
        self.instance_count = int(os.getenv('INSTANCE_COUNT', '1'))
        self.timeout_seconds = int(os.getenv('TIMEOUT_SECONDS', '1800'))  # 30 minutes

        # Validate required configuration
        required_fields = [
            ('subscription_id', self.subscription_id),
            ('resource_group', self.resource_group),
            ('workspace_name', self.workspace_name),
            ('model_name', self.model_name),
            ('env_name', self.env_name),
            ('endpoint_name', self.endpoint_name),
            ('deployment_name', self.deployment_name),
        ]

        for field_name, field_value in required_fields:
            if not field_value or not field_value.strip():
                raise AzureMLDeploymentError(f"Required configuration '{field_name}' is missing or empty")

        logger.info('Configuration loaded successfully')
        logger.debug(f'Subscription ID: {self.subscription_id}')
        logger.debug(f'Resource Group: {self.resource_group}')
        logger.debug(f'Workspace: {self.workspace_name}')
        logger.debug(f'Model: {self.model_name}')
        logger.debug(f'Environment: {self.env_name}')
        logger.debug(f'Endpoint: {self.endpoint_name}')
        logger.debug(f'Deployment: {self.deployment_name}')

    @property
    def client(self) -> MLClient:
        """Lazy-loaded ML client with connection validation."""
        if self._client is None:
            self._client = self._create_ml_client()
        return self._client

    def _create_ml_client(self) -> MLClient:
        """
        Create and validate Azure ML client connection.

        Migrated from: InteractiveLoginAuthentication + Workspace
        To: DefaultAzureCredential + MLClient

        Returns:
            Authenticated MLClient instance

        Raises:
            AzureMLDeploymentError: If client creation or authentication fails
        """
        try:
            logger.info('Creating Azure ML client connection (migrated from v1 InteractiveLoginAuthentication)')

            # Use DefaultAzureCredential for secure authentication (replaces InteractiveLoginAuthentication)
            credential = DefaultAzureCredential()

            client = MLClient(
                credential=credential,
                subscription_id=self.subscription_id,
                resource_group_name=self.resource_group,
                workspace_name=self.workspace_name,
            )

            # Validate connection by attempting to get workspace info
            workspace = client.workspaces.get(self.workspace_name)
            workspace_name = getattr(workspace, 'name', self.workspace_name)
            logger.info(f'Successfully connected to workspace: {workspace_name}')

            return client

        except Exception as e:
            error_msg = f'Failed to create Azure ML client: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def register_model(self) -> Model:
        """
        Register or retrieve existing model.

        Migrated from: Model.register() and Model(workspace=ws, name='...')
        To: MLClient.models.create_or_update() and MLClient.models.get()

        Returns:
            Registered Model instance

        Raises:
            AzureMLDeploymentError: If model registration fails
        """
        try:
            logger.info(f'Registering/retrieving model: {self.model_name} (migrated from v1 Model.register)')

            # Try to get existing model first (equivalent to Model(workspace=ws, name='...'))
            try:
                model = self.client.models.get(name=self.model_name, label='latest')
                logger.info(f'Found existing model: {model.name} (version: {model.version})')
                return model

            except ResourceNotFoundError:
                logger.info(f'Model {self.model_name} not found, creating new registration')

            # Validate model file path (equivalent to original model_path)
            model_path = Path('resources/models').resolve()
            if not model_path.exists():
                raise AzureMLDeploymentError(f'Model directory not found: {model_path}')

            # Create new model registration (replaces Model.register())
            file_model = Model(
                name=self.model_name,
                path=str(model_path),
                type=AssetTypes.CUSTOM_MODEL,
                description=f'Model registered on {pendulum.now().isoformat()} (migrated from v1)',
            )

            logger.info(f'Registering new model from path: {model_path}')
            model = self.client.models.create_or_update(file_model)
            logger.info(f'Successfully registered model: {model.name} (version: {model.version})')

            return model

        except Exception as e:
            error_msg = f'Failed to register model {self.model_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def create_environment(self) -> Environment:
        """
        Create or retrieve existing environment.

        Migrated from: Environment.from_conda_specification() and Environment.get()
        To: Environment with conda_file and MLClient.environments.create_or_update()

        Returns:
            Environment instance

        Raises:
            AzureMLDeploymentError: If environment creation fails
        """
        try:
            logger.info(f'Creating/retrieving environment: {self.env_name} (migrated from v1 Environment.get)')

            # Try to get existing environment first (equivalent to Environment.get())
            try:
                env = self.client.environments.get(name=self.env_name, label='latest')
                logger.info(f'Found existing environment: {env.name} (version: {env.version})')
                return env

            except ResourceNotFoundError:
                logger.info(f'Environment {self.env_name} not found, creating new one')

            # Validate conda file path (equivalent to original tstarc_deploy_conda.yaml)
            conda_file_path = Path('configs/ps-dev-claimsauto-tstarc.yaml').resolve()
            if not conda_file_path.exists():
                raise AzureMLDeploymentError(f'Conda file not found: {conda_file_path}')

            # Create new environment (replaces Environment.from_conda_specification())
            env_conda = Environment(
                image='mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04',
                conda_file=str(conda_file_path),
                name=self.env_name,
                description=f'Environment created on {pendulum.now().isoformat()} (migrated from v1)',
            )

            logger.info(f'Creating new environment with conda file: {conda_file_path}')
            env = self.client.environments.create_or_update(env_conda)
            logger.info(f'Successfully created environment: {env.name} (version: {env.version})')

            return env

        except Exception as e:
            error_msg = f'Failed to create environment {self.env_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def delete_existing_service(self) -> None:
        """
        Delete existing service if it exists.

        Migrated from: AksWebservice(name='...', workspace=ws).delete()
        To: MLClient.online_endpoints.begin_delete() and MLClient.online_deployments.begin_delete()

        This maintains the same workflow as the original v1 script.
        """
        try:
            logger.info(f'Checking for existing endpoint/deployment: {self.endpoint_name}')

            # Check if endpoint exists and delete it (equivalent to AksWebservice.delete())
            try:
                endpoint = self.client.online_endpoints.get(name=self.endpoint_name)
                logger.info(f'Found existing endpoint: {endpoint.name}, deleting...')

                # Delete endpoint (this will also delete all deployments)
                delete_poller = self.client.online_endpoints.begin_delete(name=self.endpoint_name)

                # Wait for deletion to complete
                logger.info('Waiting for endpoint deletion to complete...')
                while not delete_poller.done():
                    time.sleep(5)

                logger.info('Existing endpoint deleted successfully')

            except ResourceNotFoundError:
                logger.info('No existing endpoint found')

        except Exception as e:
            error_msg = f'Failed to delete existing service: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def create_endpoint(self) -> ManagedOnlineEndpoint:
        """
        Create managed online endpoint.

        Migrated from: AksCompute(workspace=ws, name=cluster_name) deployment target
        To: ManagedOnlineEndpoint (managed infrastructure)

        Returns:
            ManagedOnlineEndpoint instance

        Raises:
            AzureMLDeploymentError: If endpoint creation fails
        """
        try:
            logger.info(f'Creating endpoint: {self.endpoint_name} (migrated from v1 AksCompute target)')

            # Create new endpoint (replaces AksCompute deployment target)
            endpoint = ManagedOnlineEndpoint(
                name=self.endpoint_name,
                auth_mode='key',
                description=f'Endpoint created on {pendulum.now().isoformat()} (migrated from v1 AKS)',
                tags={'MigratedFrom': 'v1_AksWebservice', 'CreatedBy': 'AzureMLV1MigratedDeployer'},
            )

            logger.info(f'Creating new endpoint: {self.endpoint_name}')
            poller = self.client.online_endpoints.begin_create_or_update(endpoint)

            # Wait for endpoint creation with timeout
            start_time = time.time()
            while not poller.done():
                elapsed = time.time() - start_time
                if elapsed > self.timeout_seconds:
                    raise AzureMLDeploymentError(f'Endpoint creation timed out after {self.timeout_seconds} seconds')

                if int(elapsed) % 30 == 0 and elapsed > 0:
                    logger.info(f'Endpoint creation in progress... ({elapsed:.0f}s elapsed)')

                time.sleep(5)

            # Retrieve the created endpoint
            created_endpoint = self.client.online_endpoints.get(name=self.endpoint_name)
            logger.info(f'Successfully created endpoint: {created_endpoint.name}')

            # Ensure we return a ManagedOnlineEndpoint
            if isinstance(created_endpoint, ManagedOnlineEndpoint):
                return created_endpoint
            else:
                # Convert to ManagedOnlineEndpoint
                managed_endpoint = ManagedOnlineEndpoint(
                    name=created_endpoint.name,
                    auth_mode=getattr(created_endpoint, 'auth_mode', 'key'),
                    description=getattr(created_endpoint, 'description', ''),
                    tags=getattr(created_endpoint, 'tags', {}),
                )
                return managed_endpoint

        except Exception as e:
            error_msg = f'Failed to create endpoint {self.endpoint_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def create_deployment(self, model: Model, environment: Environment) -> ManagedOnlineDeployment:
        """
        Create managed online deployment.

        Migrated from: Model.deploy() with InferenceConfig and AksWebservice.deploy_configuration()
        To: ManagedOnlineDeployment with CodeConfiguration

        Args:
            model: Registered model instance
            environment: Environment instance

        Returns:
            ManagedOnlineDeployment instance

        Raises:
            AzureMLDeploymentError: If deployment creation fails
        """
        try:
            # Validate scoring script path (equivalent to original entry_script='score.py')
            scoring_script_path = Path('Python/score.py').resolve()
            code_path = scoring_script_path.parent

            if not scoring_script_path.exists():
                raise AzureMLDeploymentError(f'Scoring script not found: {scoring_script_path}')

            logger.info(f'Creating deployment: {self.deployment_name} (migrated from v1 Model.deploy)')
            logger.info(f'Using model: {model.name} (version: {model.version})')
            logger.info(f'Using environment: {environment.name} (version: {environment.version})')
            logger.info(f'Using code path: {code_path}')

            # Create deployment configuration (replaces InferenceConfig + AksWebservice.deploy_configuration)
            deployment = ManagedOnlineDeployment(
                name=self.deployment_name,
                endpoint_name=self.endpoint_name,
                model=model,
                # CodeConfiguration replaces InferenceConfig
                code_configuration=CodeConfiguration(
                    code=str(code_path),
                    scoring_script='score.py'  # equivalent to entry_script
                ),
                environment=environment,
                # Instance configuration (equivalent to cpu_cores=1, memory_gb=1.5)
                instance_type=self.instance_type,  # Standard_DS3_v2 provides similar resources
                instance_count=self.instance_count,
                # Environment variables for scoring script
                environment_variables={'PYTHONPATH': '/var/azureml-app/autolodge_retrained_deploy'},
                tags={
                    'DeploymentDate': pendulum.now().isoformat(),
                    'ModelName': model.name,
                    'ModelVersion': model.version,
                    'EnvironmentName': environment.name,
                    'EnvironmentVersion': environment.version,
                    'MigratedFrom': 'v1_AksWebservice',
                    'CreatedBy': 'AzureMLV1MigratedDeployer',
                },
            )

            # Start deployment (equivalent to Model.deploy())
            logger.info('Starting deployment creation...')
            deployment_poller = self.client.online_deployments.begin_create_or_update(deployment)

            # Wait for deployment completion with timeout (equivalent to service.wait_for_deployment())
            start_time = time.time()
            while not deployment_poller.done():
                elapsed = time.time() - start_time
                if elapsed > self.timeout_seconds:
                    raise AzureMLDeploymentError(f'Deployment creation timed out after {self.timeout_seconds} seconds')

                if int(elapsed) % 30 == 0 and elapsed > 0:
                    logger.info(f'Deployment creation in progress... ({elapsed:.0f}s elapsed)')

                time.sleep(5)

            # Retrieve the created deployment
            created_deployment = self.client.online_deployments.get(
                name=self.deployment_name, endpoint_name=self.endpoint_name
            )

            logger.info(f'Successfully created deployment: {created_deployment.name}')

            # Ensure we return a ManagedOnlineDeployment
            if isinstance(created_deployment, ManagedOnlineDeployment):
                return created_deployment
            else:
                # This should not happen in practice, but handle it for type safety
                logger.warning('Retrieved deployment is not a ManagedOnlineDeployment, returning original...')
                return deployment

        except Exception as e:
            error_msg = f'Failed to create deployment {self.deployment_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def configure_traffic(self) -> None:
        """
        Configure traffic routing to the deployment.

        This is a new feature in v2 that wasn't present in v1 AksWebservice,
        but is required for managed online endpoints.
        """
        try:
            logger.info(f'Configuring traffic for deployment: {self.deployment_name}')

            # Get the endpoint and configure traffic
            endpoint = self.client.online_endpoints.get(name=self.endpoint_name)
            endpoint.traffic = {self.deployment_name: 100}

            # Apply traffic configuration
            traffic_poller = self.client.online_endpoints.begin_create_or_update(endpoint)

            # Wait for traffic configuration completion
            start_time = time.time()
            while not traffic_poller.done():
                elapsed = time.time() - start_time
                if elapsed > 300:  # 5 minute timeout for traffic config
                    raise AzureMLDeploymentError('Traffic configuration timed out after 300 seconds')
                time.sleep(3)

            # Verify traffic configuration
            updated_endpoint = self.client.online_endpoints.get(name=self.endpoint_name)
            traffic_config = updated_endpoint.traffic

            logger.info(f'Traffic configuration completed: {traffic_config}')

        except Exception as e:
            error_msg = f'Failed to configure traffic for {self.deployment_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def get_deployment_state(self) -> str:
        """
        Get deployment state.

        Migrated from: service.state
        To: deployment provisioning_state

        Returns:
            Deployment state string
        """
        try:
            deployment = self.client.online_deployments.get(
                name=self.deployment_name, endpoint_name=self.endpoint_name
            )
            state = getattr(deployment, 'provisioning_state', 'Unknown')
            logger.info(f'Deployment state: {state}')
            return state

        except Exception as e:
            error_msg = f'Failed to get deployment state: {str(e)}'
            logger.error(error_msg)
            return 'Error'

    def deploy(self) -> None:
        """
        Execute the complete deployment process.

        This method maintains the same workflow as the original v1 script:
        1. Connect to workspace
        2. Register/retrieve model
        3. Create/retrieve environment
        4. Delete existing service
        5. Deploy model
        6. Wait for deployment
        7. Print state
        """
        try:
            logger.info('=== Starting Azure ML Deployment (Migrated from v1) ===')

            # Step 1: Connect to workspace (equivalent to original auth + ws setup)
            logger.info('Step 1: Connecting to workspace...')
            _ = self.client  # This triggers client creation and validation

            # Step 2: Register/retrieve model (equivalent to Model.register() or Model())
            logger.info('Step 2: Registering/retrieving model...')
            model = self.register_model()

            # Step 3: Create/retrieve environment (equivalent to Environment.from_conda_specification() or Environment.get())
            logger.info('Step 3: Creating/retrieving environment...')
            environment = self.create_environment()

            # Step 4: Delete existing service (equivalent to AksWebservice.delete())
            logger.info('Step 4: Deleting existing service if present...')
            self.delete_existing_service()

            # Step 5: Create endpoint (replaces AksCompute target)
            logger.info('Step 5: Creating endpoint...')
            endpoint = self.create_endpoint()

            # Step 6: Deploy model (equivalent to Model.deploy())
            logger.info('Step 6: Deploying model...')
            deployment = self.create_deployment(model, environment)

            # Step 7: Configure traffic (new in v2)
            logger.info('Step 7: Configuring traffic...')
            self.configure_traffic()

            # Step 8: Get deployment state (equivalent to service.state)
            logger.info('Step 8: Getting deployment state...')
            state = self.get_deployment_state()

            # Final summary
            logger.info('=' * 60)
            logger.info('DEPLOYMENT SUMMARY')
            logger.info('=' * 60)
            logger.info(f'Endpoint: {endpoint.name}')
            logger.info(f'Deployment: {deployment.name}')
            logger.info(f'Model: {model.name} (version: {model.version})')
            logger.info(f'Environment: {environment.name} (version: {environment.version})')
            logger.info(f'Instance Type: {self.instance_type}')
            logger.info(f'Instance Count: {self.instance_count}')
            logger.info(f'State: {state}')
            logger.info('=' * 60)

            logger.info('=== Azure ML Deployment Completed Successfully ===')

        except Exception as e:
            error_msg = f'Deployment failed: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e


def main() -> None:
    """
    Main entry point for the migrated deployment script.

    This maintains the same execution flow as the original v1 script
    but uses modern Azure ML SDK v2 patterns.
    """
    try:
        # Create deployer instance
        deployer = AzureMLV1MigratedDeployer()

        # Execute deployment
        deployer.deploy()

        logger.info('Deployment script completed successfully!')

    except AzureMLDeploymentError as e:
        logger.error(f'Deployment failed: {str(e)}')
        sys.exit(1)
    except KeyboardInterrupt:
        logger.warning('Deployment interrupted by user')
        sys.exit(130)
    except Exception as e:
        logger.error(f'Unexpected error during deployment: {str(e)}')
        sys.exit(1)


if __name__ == '__main__':
    main()